
import 'https://deno.land/x/xhr@0.1.0/mod.ts'
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'

const openRouterApiKey = Deno.env.get('OPENROUTER_API_KEY')

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    if (!openRouterApiKey) {
      throw new Error('OPENROUTER_API_KEY is not available in the environment.')
    }

    const { word, context } = await req.json()
    if (!word) {
      throw new Error('Word is required.')
    }
    if (!context) {
      throw new Error('Context is required.')
    }

    // Using OpenRouter to get definition
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openRouterApiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://lovable.dev', // Required by OpenRouter
        'X-Title': 'English Learning Assistant', // Optional but good practice
      },
      body: JSON.stringify({
        model: 'openai/gpt-4o', // A reliable model for JSON output
        messages: [
          {
            role: 'system',
            content: `You are an English dictionary assistant. For a given word and its context sentence, provide a concise definition, its part of speech, a new and simple example sentence showing how the word is used, phonetic transcription (IPA), the base form of the word (e.g., for 'running' it's 'run'), a Chinese translation of the word, and a Chinese translation of the new example sentence.
Respond ONLY with a valid JSON object with the following keys: "word", "definition", "partOfSpeech", "example", "phonetic", "chineseDefinition", "chineseExample", "baseForm". The "chineseExample" should be the translation of the new "example" sentence. The "word" field should contain the original word provided by the user. If the provided word is already in its base form, the "baseForm" field should contain the word itself. If no phonetic transcription is available, return an empty string for "phonetic".`,
          },
          { role: 'user', content: JSON.stringify({ word, sentence: context }) },
        ],
        response_format: { type: 'json_object' },
        temperature: 0.3, // Lower temperature for more deterministic definition
        max_tokens: 300,
      }),
    })

    if (!response.ok) {
      const error = await response.text()
      console.error('OpenRouter API error:', error)
      throw new Error(`OpenRouter API error: ${error}`)
    }

    const data = await response.json()
    const responseContent = data.choices[0].message.content
    if (!responseContent) {
      throw new Error('Failed to get definition from AI.')
    }

    const definitionData = JSON.parse(responseContent)

    return new Response(JSON.stringify(definitionData), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    })
  } catch (error) {
    console.error('Error in get-word-definition function:', error)
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  }
})
