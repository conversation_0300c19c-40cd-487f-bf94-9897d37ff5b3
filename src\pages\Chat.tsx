
import React from 'react';
import { MessageCircle, Sparkles, Book } from 'lucide-react';
import AIChat from '@/components/AIChat';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import VocabularyList from '@/components/VocabularyList';

const Chat = () => {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <MessageCircle className="w-8 h-8 text-blue-600" />
            <Sparkles className="w-6 h-6 text-emerald-600" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            AI 英语对话
          </h1>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
            <p className="text-xl text-gray-600 max-w-2xl">
              与专业的 AI 英语助手对话，获得个性化的学习指导和答疑解惑
            </p>
            <Dialog>
              <DialogTrigger asChild>
                <Button>
                  <Book className="mr-2 h-4 w-4" />
                  我的生词本
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-3xl w-full h-[80vh] flex flex-col p-6">
                <DialogHeader>
                  <DialogTitle>我的生词本</DialogTitle>
                </DialogHeader>
                <div className="flex-1 overflow-hidden mt-4">
                  <VocabularyList />
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Features */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
              <MessageCircle className="w-6 h-6 text-blue-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">智能对话</h3>
            <p className="text-gray-600">AI 助手理解你的问题，提供准确的英语学习建议</p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center mb-4">
              <Sparkles className="w-6 h-6 text-emerald-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">即时反馈</h3>
            <p className="text-gray-600">实时获得语法纠正、词汇解释和学习指导</p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
              <MessageCircle className="w-6 h-6 text-purple-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">个性化学习</h3>
            <p className="text-gray-600">根据你的水平和需求，提供定制化的学习内容</p>
          </div>
        </div>

        {/* Chat Interface */}
        <div>
          <AIChat />
        </div>

        {/* Tips */}
        <div className="mt-8 bg-gradient-to-r from-blue-50 to-emerald-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">使用技巧</h3>
          <ul className="space-y-2 text-gray-600">
            <li>• 可以询问语法问题：如 "什么时候用现在完成时？"</li>
            <li>• 请求词汇解释：如 "explain 和 describe 有什么区别？"</li>
            <li>• 寻求发音帮助：如 "这个单词怎么发音？"</li>
            <li>• 练习对话：如 "我想练习餐厅点餐的对话"</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default Chat;
