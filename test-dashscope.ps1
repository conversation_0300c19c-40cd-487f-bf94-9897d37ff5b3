# DashScope API 测试脚本
# 使用方法: .\test-dashscope.ps1 -ApiKey "your-api-key-here"

param(
    [Parameter(Mandatory=$true)]
    [string]$ApiKey
)

Write-Host "=== DashScope API 连接测试 ===" -ForegroundColor Cyan
Write-Host "API Key: $($ApiKey.Substring(0,10))..." -ForegroundColor Yellow

# 测试文本生成
function Test-TextGeneration {
    param([string]$ApiKey)
    
    Write-Host "`n🤖 测试文本生成..." -ForegroundColor Green
    
    $headers = @{
        'Authorization' = "Bearer $ApiKey"
        'Content-Type' = 'application/json'
    }

    $body = @{
        model = "qwen-turbo"
        input = @{
            messages = @(
                @{
                    role = "user"
                    content = "你好，请简单介绍一下你自己。"
                }
            )
        }
        parameters = @{
            temperature = 0.7
            max_tokens = 100
        }
    } | ConvertTo-Json -Depth 10

    try {
        $response = Invoke-RestMethod -Uri 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation' -Method POST -Headers $headers -Body $body -TimeoutSec 30
        
        if ($response.output -and $response.output.choices) {
            Write-Host "✅ 文本生成成功！" -ForegroundColor Green
            Write-Host "响应内容: $($response.output.choices[0].message.content)" -ForegroundColor White
            return $true
        } else {
            Write-Host "❌ 文本生成失败：响应格式异常" -ForegroundColor Red
            Write-Host "完整响应: $($response | ConvertTo-Json -Depth 5)" -ForegroundColor Yellow
            return $false
        }
    } catch {
        Write-Host "❌ 文本生成失败！" -ForegroundColor Red
        Write-Host "错误信息: $($_.Exception.Message)" -ForegroundColor Red
        
        if ($_.ErrorDetails.Message) {
            try {
                $errorDetail = $_.ErrorDetails.Message | ConvertFrom-Json
                Write-Host "错误代码: $($errorDetail.code)" -ForegroundColor Yellow
                Write-Host "错误详情: $($errorDetail.message)" -ForegroundColor Yellow
            } catch {
                Write-Host "错误详情: $($_.ErrorDetails.Message)" -ForegroundColor Yellow
            }
        }
        return $false
    }
}

# 测试图像生成
function Test-ImageGeneration {
    param([string]$ApiKey)
    
    Write-Host "`n🎨 测试图像生成..." -ForegroundColor Green
    
    $headers = @{
        'Authorization' = "Bearer $ApiKey"
        'Content-Type' = 'application/json'
    }

    $body = @{
        model = "wanx-v1"
        input = @{
            prompt = "a beautiful sunset over mountains"
        }
        parameters = @{
            size = "1024*1024"
            n = 1
        }
    } | ConvertTo-Json -Depth 10

    try {
        $response = Invoke-RestMethod -Uri 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis' -Method POST -Headers $headers -Body $body -TimeoutSec 60
        
        if ($response.output -and $response.output.results) {
            Write-Host "✅ 图像生成成功！" -ForegroundColor Green
            Write-Host "图像URL: $($response.output.results[0].url)" -ForegroundColor White
            return $true
        } else {
            Write-Host "❌ 图像生成失败：响应格式异常" -ForegroundColor Red
            Write-Host "完整响应: $($response | ConvertTo-Json -Depth 5)" -ForegroundColor Yellow
            return $false
        }
    } catch {
        Write-Host "❌ 图像生成失败！" -ForegroundColor Red
        Write-Host "错误信息: $($_.Exception.Message)" -ForegroundColor Red
        
        if ($_.ErrorDetails.Message) {
            try {
                $errorDetail = $_.ErrorDetails.Message | ConvertFrom-Json
                Write-Host "错误代码: $($errorDetail.code)" -ForegroundColor Yellow
                Write-Host "错误详情: $($errorDetail.message)" -ForegroundColor Yellow
            } catch {
                Write-Host "错误详情: $($_.ErrorDetails.Message)" -ForegroundColor Yellow
            }
        }
        return $false
    }
}

# 测试语音合成
function Test-AudioGeneration {
    param([string]$ApiKey)
    
    Write-Host "`n🎵 测试语音合成..." -ForegroundColor Green
    
    $headers = @{
        'Authorization' = "Bearer $ApiKey"
        'Content-Type' = 'application/json'
    }

    $body = @{
        model = "qwen-tts-v1"
        input = @{
            text = "Hello, this is a test for text to speech."
        }
        parameters = @{
            voice = "zhichu"
            format = "mp3"
        }
    } | ConvertTo-Json -Depth 10

    try {
        $response = Invoke-RestMethod -Uri 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text2speech/speech-synthesis' -Method POST -Headers $headers -Body $body -TimeoutSec 30
        
        if ($response.output -and $response.output.audio_url) {
            Write-Host "✅ 语音合成成功！" -ForegroundColor Green
            Write-Host "音频URL: $($response.output.audio_url)" -ForegroundColor White
            return $true
        } else {
            Write-Host "❌ 语音合成失败：响应格式异常" -ForegroundColor Red
            Write-Host "完整响应: $($response | ConvertTo-Json -Depth 5)" -ForegroundColor Yellow
            return $false
        }
    } catch {
        Write-Host "❌ 语音合成失败！" -ForegroundColor Red
        Write-Host "错误信息: $($_.Exception.Message)" -ForegroundColor Red
        
        if ($_.ErrorDetails.Message) {
            try {
                $errorDetail = $_.ErrorDetails.Message | ConvertFrom-Json
                Write-Host "错误代码: $($errorDetail.code)" -ForegroundColor Yellow
                Write-Host "错误详情: $($errorDetail.message)" -ForegroundColor Yellow
            } catch {
                Write-Host "错误详情: $($_.ErrorDetails.Message)" -ForegroundColor Yellow
            }
        }
        return $false
    }
}

# 主测试流程
Write-Host "`n开始测试 DashScope API 连接..." -ForegroundColor Cyan

$results = @{
    Text = Test-TextGeneration -ApiKey $ApiKey
    Image = Test-ImageGeneration -ApiKey $ApiKey
    Audio = Test-AudioGeneration -ApiKey $ApiKey
}

# 输出测试结果
Write-Host "`n=== 测试结果汇总 ===" -ForegroundColor Cyan
Write-Host "文本生成: $(if($results.Text){'✅ 成功'}else{'❌ 失败'})" -ForegroundColor $(if($results.Text){'Green'}else{'Red'})
Write-Host "图像生成: $(if($results.Image){'✅ 成功'}else{'❌ 失败'})" -ForegroundColor $(if($results.Image){'Green'}else{'Red'})
Write-Host "语音合成: $(if($results.Audio){'✅ 成功'}else{'❌ 失败'})" -ForegroundColor $(if($results.Audio){'Green'}else{'Red'})

$successCount = ($results.Values | Where-Object {$_}).Count
Write-Host "`n总体结果: $successCount/3 项测试通过" -ForegroundColor $(if($successCount -eq 3){'Green'}elseif($successCount -gt 0){'Yellow'}else{'Red'})

if ($successCount -eq 3) {
    Write-Host "`n🎉 恭喜！所有服务都可以正常使用！" -ForegroundColor Green
    Write-Host "您可以在应用中使用以下配置：" -ForegroundColor Cyan
    Write-Host "- 文本模型: qwen-turbo" -ForegroundColor White
    Write-Host "- 图像模型: wanx-v1" -ForegroundColor White
    Write-Host "- 语音模型: qwen-tts-v1" -ForegroundColor White
} elseif ($successCount -gt 0) {
    Write-Host "`n⚠️  部分服务可用，请检查失败的服务配置。" -ForegroundColor Yellow
} else {
    Write-Host "`n❌ 所有服务都无法使用，请检查：" -ForegroundColor Red
    Write-Host "1. API Key 是否正确" -ForegroundColor White
    Write-Host "2. 是否已开通相应服务" -ForegroundColor White
    Write-Host "3. 网络连接是否正常" -ForegroundColor White
}

Write-Host "`n测试完成！" -ForegroundColor Cyan
