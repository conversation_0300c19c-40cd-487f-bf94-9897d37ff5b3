import { useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';

interface UseAudioHandlersProps {
  audioRef: React.RefObject<HTMLAudioElement>;
  isPlaying: boolean;
  setIsPlaying: (playing: boolean) => void;
  setCurrentTime: (time: number) => void;
  setDuration: (duration: number) => void;
  isLooping: boolean;
  loopStart: number | null;
  loopEnd: number | null;
  currentExercise: number;
  setPlaybackRate: (rate: number) => void;
  setLoopStart: (time: number | null) => void;
  setLoopEnd: (time: number | null) => void;
  audioFiles: { [key: number]: string };
  playbackRate: number;
  setIsUploading: (uploading: boolean) => void;
  isGenerating: boolean;
  setIsGenerating: (generating: boolean) => void;
  setAudioFiles: (updater: (prev: { [key: number]: string; }) => { [key: number]: string; }) => void;
  setIsLooping: (looping: boolean) => void;
}

export const useAudioHandlers = ({
  audioRef,
  isPlaying,
  setIsPlaying,
  setCurrentTime,
  setDuration,
  isLooping,
  loopStart,
  loopEnd,
  currentExercise,
  setPlaybackRate,
  setLoopStart,
  setLoopEnd,
  audioFiles,
  playbackRate,
  setIsUploading,
  isGenerating,
  setIsGenerating,
  setAudioFiles,
  setIsLooping
}: UseAudioHandlersProps) => {
  const { toast } = useToast();

  const hasAudioForCurrentExercise = audioFiles[currentExercise] !== undefined;

  const handleAudioUpload = async (file: File) => {
    setIsUploading(true);
    try {
      const audioUrl = URL.createObjectURL(file);
      setAudioFiles(prev => ({
        ...prev,
        [currentExercise]: audioUrl
      }));
      toast({
        title: "音频上传成功",
        description: "您现在可以开始听力练习了"
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "音频上传失败",
        description: "请重试"
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleGenerateAudio = async (text: string) => {
    setIsGenerating(true);
    try {
      toast({
        title: "正在生成音频...",
        description: "请稍候，AI正在为您生成音频文件。"
      });
      const { data, error } = await supabase.functions.invoke('text-to-speech', {
        body: { text },
      });

      if (error || !data.audioContent) {
        throw new Error(error?.message || "无法生成音频");
      }

      const audioBlob = await (await fetch(`data:audio/${data.format};base64,${data.audioContent}`)).blob();
      const audioUrl = URL.createObjectURL(audioBlob);

      setAudioFiles(prev => ({
        ...prev,
        [currentExercise]: audioUrl
      }));

      toast({
        title: "音频生成成功",
        description: "您现在可以开始听力练习了"
      });
    } catch (err: any) {
      console.error('TTS Error:', err);
      toast({
        variant: "destructive",
        title: "音频生成失败",
        description: err.message || "请检查网络或稍后重试",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleRemoveAudio = () => {
    if (audioFiles[currentExercise]) {
      URL.revokeObjectURL(audioFiles[currentExercise]);
      setAudioFiles(prev => {
        const newFiles = { ...prev };
        delete newFiles[currentExercise];
        return newFiles;
      });
    }
    resetAudio();
  };
  
  const handleToggleLoop = () => {
    setIsLooping(!isLooping);
  };

  const togglePlayPause = async () => {
    if (!hasAudioForCurrentExercise) return;

    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.src = audioFiles[currentExercise];
        audioRef.current.playbackRate = playbackRate;
        await audioRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const resetAudio = () => {
    setCurrentTime(0);
    setIsPlaying(false);
    if (audioRef.current) {
      audioRef.current.currentTime = 0;
      audioRef.current.pause();
    }
  };

  const handlePlaybackRateChange = (rate: number) => {
    setPlaybackRate(rate);
    if (audioRef.current) {
      audioRef.current.playbackRate = rate;
    }
  };

  const handleSetLoopStart = (currentTime: number) => {
    setLoopStart(currentTime);
    toast({
      title: "A点已设置",
      description: `循环开始点: ${Math.floor(currentTime / 60)}:${(currentTime % 60).toFixed(0).padStart(2, '0')}`
    });
  };

  const handleSetLoopEnd = (currentTime: number) => {
    if (loopStart !== null && currentTime > loopStart) {
      setLoopEnd(currentTime);
      toast({
        title: "B点已设置",
        description: `循环结束点: ${Math.floor(currentTime / 60)}:${(currentTime % 60).toFixed(0).padStart(2, '0')}`
      });
    } else {
      toast({
        variant: "destructive",
        title: "设置失败",
        description: "B点必须在A点之后"
      });
    }
  };

  const handleClearLoop = () => {
    setLoopStart(null);
    setLoopEnd(null);
    toast({
      title: "循环已清除",
      description: "AB循环点已重置"
    });
  };

  // Audio event handlers
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleTimeUpdate = () => {
      const time = audio.currentTime;
      setCurrentTime(time);
      
      // Handle AB loop
      if (isLooping && loopStart !== null && loopEnd !== null) {
        if (time >= loopEnd) {
          audio.currentTime = loopStart;
        }
      }
    };

    const handleLoadedMetadata = () => setDuration(audio.duration);
    const handleEnded = () => {
      if (isLooping && loopStart !== null && loopEnd !== null) {
        audio.currentTime = loopStart;
        audio.play();
      } else {
        setIsPlaying(false);
      }
    };
    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);

    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('play', handlePlay);
    audio.addEventListener('pause', handlePause);

    return () => {
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('play', handlePlay);
      audio.removeEventListener('pause', handlePause);
    };
  }, [isLooping, loopStart, loopEnd, setCurrentTime, setDuration, setIsPlaying]);

  // Reset audio when exercise changes
  useEffect(() => {
    setIsPlaying(false);
    setCurrentTime(0);
    setDuration(0);
    setPlaybackRate(1);
    setIsLooping(false);
    setLoopStart(null);
    setLoopEnd(null);
    if (audioRef.current) {
      audioRef.current.src = '';
    }
  }, [currentExercise, setIsPlaying, setCurrentTime, setDuration, setPlaybackRate, setIsLooping, setLoopStart, setLoopEnd]);

  return {
    hasAudioForCurrentExercise,
    handleAudioUpload,
    handleGenerateAudio,
    togglePlayPause,
    resetAudio,
    handlePlaybackRateChange,
    handleSetLoopStart,
    handleSetLoopEnd,
    handleClearLoop,
    handleRemoveAudio,
    handleToggleLoop
  };
};
