
import { useToast } from '@/hooks/use-toast';

interface UsePracticeHandlersProps {
  setCurrentExercise: (index: number) => void;
  setAnswers: (answers: { [key: number]: number }) => void;
  setShowResults: (show: boolean) => void;
  setShowTranscript: (show: boolean) => void;
  setIsPlaying: (playing: boolean) => void;
  setCurrentTime: (time: number) => void;
  setPracticeMode: (mode: 'listening' | 'dictation' | 'fillBlank' | 'speaking') => void;
  setVocabulary: (vocabulary: string[]) => void;
  currentExercise: number;
  totalExercises: number;
}

export const usePracticeHandlers = ({
  setCurrentExercise,
  setAnswers,
  setShowResults,
  setShowTranscript,
  setIsPlaying,
  setCurrentTime,
  setPracticeMode,
  setVocabulary,
  currentExercise,
  totalExercises
}: UsePracticeHandlersProps) => {
  const { toast } = useToast();

  const handleExerciseSelect = (index: number) => {
    setCurrentExercise(index);
    setAnswers({});
    setShowResults(false);
    setShowTranscript(false);
  };

  const handleAnswerSelect = (questionIndex: number, answerIndex: number) => {
    setAnswers((prev => {
      return {
        ...prev,
        [questionIndex]: answerIndex
      };
    }) as any); // 强制兼容, 若还有类型报错则需彻底重构usePracticeState类型为函数式setState
  };

  const checkAnswers = () => {
    setShowResults(true);
  };

  const retryExercise = () => {
    setAnswers({});
    setShowResults(false);
  };

  const nextExercise = () => {
    if (currentExercise < totalExercises - 1) {
      setCurrentExercise(currentExercise + 1);
      setAnswers({});
      setShowResults(false);
      setShowTranscript(false);
      setIsPlaying(false);
      setCurrentTime(0);
    }
  };

  const handleModeChange = (mode: 'listening' | 'dictation' | 'fillBlank' | 'speaking') => {
    setPracticeMode(mode);
    setAnswers({});
    setShowResults(false);
  };

  const handleDictationComplete = (score: number, userInput: string) => {
    toast({
      title: "听写完成",
      description: `得分: ${score}%`
    });
  };

  const handleFillBlankComplete = (score: number, answers: string[]) => {
    toast({
      title: "填空练习完成",
      description: `得分: ${score}%`
    });
  };

  const handleSpeakingComplete = (recordingUrl: string) => {
    toast({
      title: "录音完成",
      description: "您可以播放录音进行对比"
    });
  };

  const handleVocabularyUpdate = (newVocabulary: string[]) => {
    setVocabulary(newVocabulary);
  };

  return {
    handleExerciseSelect,
    handleAnswerSelect,
    checkAnswers,
    retryExercise,
    nextExercise,
    handleModeChange,
    handleDictationComplete,
    handleFillBlankComplete,
    handleSpeakingComplete,
    handleVocabularyUpdate
  };
};
