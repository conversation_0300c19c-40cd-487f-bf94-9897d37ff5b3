
import React from 'react';
import { Check, Crown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useSubscription } from '@/hooks/useSubscription';

interface PricingPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  priceId: string;
  features: string[];
  popular?: boolean;
}

const PRICING_PLANS: PricingPlan[] = [
  {
    id: 'free',
    name: '免费版',
    description: '适合初学者的基础功能',
    price: 0,
    priceId: '',
    features: [
      '基础课程访问',
      '每日5次AI对话',
      '基础听力练习',
      '学习进度跟踪'
    ]
  },
  {
    id: 'pro',
    name: '专业版',
    description: '解锁全部功能的最佳选择',
    price: 29,
    priceId: 'price_pro_monthly', // 您需要在Stripe中创建这个价格ID
    popular: true,
    features: [
      '所有课程内容',
      '无限AI对话',
      '高级听力练习',
      '语音识别功能',
      '划词翻译',
      '语音合成',
      '个性化学习计划',
      '优先客户支持'
    ]
  },
  {
    id: 'premium',
    name: '企业版',
    description: '为团队和企业打造',
    price: 99,
    priceId: 'price_premium_monthly', // 您需要在Stripe中创建这个价格ID
    features: [
      '专业版所有功能',
      '团队管理功能',
      '自定义课程内容',
      'API访问权限',
      '专属客户经理',
      '数据分析报告'
    ]
  }
];

const SubscriptionCard = () => {
  const { subscription, createCheckoutSession, manageBilling } = useSubscription();

  const getCurrentTier = () => {
    if (!subscription || !subscription.subscribed) return 'free';
    return subscription.subscription_tier || 'free';
  };

  const currentTier = getCurrentTier();

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent">
          选择您的学习计划
        </h2>
        <p className="text-gray-600 mt-2">解锁更多功能，加速您的英语学习之旅</p>
      </div>

      <div className="grid md:grid-cols-3 gap-6">
        {PRICING_PLANS.map((plan) => {
          const isCurrentPlan = currentTier === plan.id;
          const isFreePlan = plan.id === 'free';
          
          return (
            <Card key={plan.id} className={`relative ${plan.popular ? 'border-blue-500 shadow-lg' : ''}`}>
              {plan.popular && (
                <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-blue-600 to-emerald-600">
                  <Crown className="w-3 h-3 mr-1" />
                  最受欢迎
                </Badge>
              )}
              
              <CardHeader className="text-center">
                <CardTitle className="text-xl">{plan.name}</CardTitle>
                <CardDescription>{plan.description}</CardDescription>
                <div className="text-3xl font-bold">
                  ¥{plan.price}
                  {!isFreePlan && <span className="text-sm text-gray-500">/月</span>}
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                <ul className="space-y-2">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-center space-x-2">
                      <Check className="w-4 h-4 text-green-500" />
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>

                {isCurrentPlan ? (
                  <Button className="w-full" disabled>
                    当前计划
                  </Button>
                ) : isFreePlan ? (
                  <Button variant="outline" className="w-full" disabled>
                    免费使用
                  </Button>
                ) : (
                  <Button 
                    className="w-full bg-gradient-to-r from-blue-600 to-emerald-600 hover:shadow-lg"
                    onClick={() => createCheckoutSession(plan.priceId)}
                  >
                    立即升级
                  </Button>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {subscription && subscription.subscribed && (
        <div className="text-center">
          <Button variant="outline" onClick={manageBilling}>
            管理账单
          </Button>
        </div>
      )}
    </div>
  );
};

export default SubscriptionCard;
