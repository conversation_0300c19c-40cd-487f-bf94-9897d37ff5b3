import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Building2,
  Utensils,
  Plane,
  ShoppingBag,
  Phone,
  GraduationCap,
  User,
  Users,
  Play,
  RotateCcw
} from 'lucide-react';

interface Scenario {
  id: string;
  icon: any;
  title: string;
  description: string;
  roles: string[];
}

const scenarios: Scenario[] = [
  {
    id: 'restaurant',
    icon: Utensils,
    title: '餐厅用餐',
    description: '练习如何点餐、询问菜品、表达特殊需求等',
    roles: ['顾客', '服务员', '厨师']
  },
  {
    id: 'airport',
    icon: Plane,
    title: '机场出行',
    description: '学习如何问路、值机、通过安检等',
    roles: ['旅客', '机场工作人员', '海关人员']
  },
  {
    id: 'shopping',
    icon: ShoppingBag,
    title: '商场购物',
    description: '掌握询问价格、尺码、退换货等对话',
    roles: ['顾客', '导购', '收银员']
  },
  {
    id: 'interview',
    icon: Building2,
    title: '求职面试',
    description: '模拟面试问答、自我介绍等场景',
    roles: ['求职者', '面试官', 'HR']
  },
  {
    id: 'school',
    icon: GraduationCap,
    title: '校园生活',
    description: '练习与同学、老师的日常交流',
    roles: ['学生', '老师', '同学']
  },
  {
    id: 'phone',
    icon: Phone,
    title: '电话沟通',
    description: '学习预约、咨询、投诉等电话用语',
    roles: ['客户', '客服', '经理']
  }
];

const Scenarios = () => {
  const [selectedScenario, setSelectedScenario] = useState<Scenario | null>(null);
  const [selectedRole, setSelectedRole] = useState<string>('');
  const [conversation, setConversation] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const startConversation = async () => {
    if (!selectedScenario || !selectedRole) return;
    setLoading(true);
    try {
      const { data, error } = await supabase.functions.invoke('chat-ai', {
        body: {
          message: `请模拟一个"${selectedScenario.title}"场景的对话。我扮演"${selectedRole}"，你扮演其他角色。请给出对话内容，并在对话后总结主要用到的表达和词汇。`,
          conversationHistory: [],
          model: 'gemini-2.0-flash-exp',
        },
      });
      if (error) throw error;
      setConversation(data.response);
    } catch (err: any) {
      setConversation('AI服务出错，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const resetScenario = () => {
    setSelectedScenario(null);
    setSelectedRole('');
    setConversation('');
  };

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-3xl font-bold mb-8 text-center">真实场景模拟</h1>

      {!selectedScenario ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {scenarios.map((scenario) => {
            const Icon = scenario.icon;
            return (
              <Card
                key={scenario.id}
                className="p-6 cursor-pointer hover:shadow-lg transition-shadow"
                onClick={() => setSelectedScenario(scenario)}
              >
                <div className="flex items-center gap-4 mb-4">
                  <div className="p-3 bg-blue-100 rounded-lg">
                    <Icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-semibold">{scenario.title}</h3>
                </div>
                <p className="text-gray-600 mb-4">{scenario.description}</p>
                <div className="flex gap-2">
                  {scenario.roles.map((role) => (
                    <span
                      key={role}
                      className="px-2 py-1 bg-gray-100 rounded text-sm"
                    >
                      {role}
                    </span>
                  ))}
                </div>
              </Card>
            );
          })}
        </div>
      ) : (
        <Card className="p-6 max-w-4xl mx-auto">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-blue-100 rounded-lg">
                {React.createElement(selectedScenario.icon, {
                  className: "w-6 h-6 text-blue-600"
                })}
              </div>
              <h2 className="text-2xl font-semibold">{selectedScenario.title}</h2>
            </div>
            <Button variant="outline" size="icon" onClick={resetScenario}>
              <RotateCcw className="w-4 h-4" />
            </Button>
          </div>

          <div className="mb-6">
            <h3 className="text-lg font-medium mb-3">选择角色</h3>
            <div className="flex gap-3">
              {selectedScenario.roles.map((role) => (
                <Button
                  key={role}
                  variant={selectedRole === role ? "default" : "outline"}
                  onClick={() => setSelectedRole(role)}
                >
                  <User className="w-4 h-4 mr-2" />
                  {role}
                </Button>
              ))}
            </div>
          </div>

          {selectedRole && (
            <div className="space-y-4">
              <Button
                className="w-full"
                onClick={startConversation}
                disabled={loading}
              >
                <Play className="w-4 h-4 mr-2" />
                {loading ? '生成对话中...' : '开始对话'}
              </Button>

              {conversation && (
                <div className="p-4 bg-gray-50 rounded-lg">
                  <div className="prose max-w-none">
                    {conversation.split('\n').map((line, i) => (
                      <p key={i}>{line}</p>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </Card>
      )}
    </div>
  );
};

export default Scenarios;
