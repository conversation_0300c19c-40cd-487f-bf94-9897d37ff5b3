import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Globe,
  Search,
  Bookmark,
  ThumbsUp,
  PlayCircle,
  ChevronRight,
  Coffee,
  Briefcase,
  Heart,
  Music,
  Film,
  Utensils
} from 'lucide-react';
import { Input } from "@/components/ui/input";

interface Category {
  id: string;
  icon: any;
  name: string;
  description: string;
}

interface IdiomItem {
  id: string;
  text: string;
  meaning: string;
  category: string;
  likes: number;
  usage: string;
}

const categories: Category[] = [
  {
    id: 'daily',
    icon: Coffee,
    name: '日常生活',
    description: '常见的生活用语和表达方式'
  },
  {
    id: 'business',
    icon: Briefcase,
    name: '商务职场',
    description: '职场中常用的专业表达'
  },
  {
    id: 'relationship',
    icon: Heart,
    name: '人际关系',
    description: '描述关系的独特表达'
  },
  {
    id: 'entertainment',
    icon: Music,
    name: '娱乐文化',
    description: '流行文化中的习语'
  },
  {
    id: 'movie',
    icon: Film,
    name: '影视用语',
    description: '电影电视中的经典台词'
  },
  {
    id: 'food',
    icon: Utensils,
    name: '饮食文化',
    description: '与美食相关的表达方式'
  }
];

const popularIdioms: IdiomItem[] = [
  {
    id: '1',
    text: 'Break a leg',
    meaning: '祝好运（演艺圈常用）',
    category: 'entertainment',
    likes: 128,
    usage: '在表演前祝福他人时使用'
  },
  {
    id: '2',
    text: 'Piece of cake',
    meaning: '很容易，小菜一碟',
    category: 'daily',
    likes: 256,
    usage: '形容任务很简单时使用'
  },
  {
    id: '3',
    text: 'Hit the nail on the head',
    meaning: '一针见血，说到点子上',
    category: 'daily',
    likes: 189,
    usage: '形容分析非常准确时使用'
  }
];

const Culture = () => {
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [explanation, setExplanation] = useState('');
  const [loading, setLoading] = useState(false);

  const getExplanation = async (text: string) => {
    setLoading(true);
    try {
      const { data, error } = await supabase.functions.invoke('chat-ai', {
        body: {
          message: `请详细解释英语习语"${text}"的含义、用法和文化背景，并给出一些实际例句。`,
          conversationHistory: [],
          model: 'gemini-2.0-flash-exp',
        },
      });
      if (error) throw error;
      setExplanation(data.response);
    } catch (err: any) {
      setExplanation('AI服务出错，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-3xl font-bold mb-8 text-center">文化习语学习</h1>

      {/* 搜索栏 */}
      <div className="max-w-xl mx-auto mb-8">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <Input
            className="pl-10"
            placeholder="搜索习语、文化表达..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {/* 分类导航 */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-8">
        {categories.map((category) => {
          const Icon = category.icon;
          return (
            <Card
              key={category.id}
              className={`p-4 cursor-pointer hover:shadow-md transition-all ${
                selectedCategory?.id === category.id ? 'ring-2 ring-blue-500' : ''
              }`}
              onClick={() => setSelectedCategory(category)}
            >
              <div className="flex flex-col items-center text-center">
                <div className="p-3 bg-blue-100 rounded-lg mb-3">
                  <Icon className="w-6 h-6 text-blue-600" />
                </div>
                <h3 className="font-medium mb-1">{category.name}</h3>
                <p className="text-xs text-gray-500">{category.description}</p>
              </div>
            </Card>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 热门习语列表 */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-6">热门习语</h2>
          <div className="space-y-4">
            {popularIdioms.map((idiom) => (
              <Card
                key={idiom.id}
                className="p-4 hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => getExplanation(idiom.text)}
              >
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h3 className="font-medium">{idiom.text}</h3>
                    <p className="text-sm text-gray-600">{idiom.meaning}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="ghost" size="icon">
                      <Bookmark className="w-4 h-4" />
                    </Button>
                    <div className="flex items-center text-gray-500">
                      <ThumbsUp className="w-4 h-4 mr-1" />
                      <span className="text-sm">{idiom.likes}</span>
                    </div>
                  </div>
                </div>
                <p className="text-sm text-gray-500">{idiom.usage}</p>
              </Card>
            ))}
          </div>
        </Card>

        {/* 详细解释 */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-6">习语解析</h2>
          {loading ? (
            <div className="text-center py-8">加载中...</div>
          ) : explanation ? (
            <div className="prose max-w-none">
              {explanation.split('\n').map((line, i) => (
                <p key={i}>{line}</p>
              ))}
            </div>
          ) : (
            <div className="text-center text-gray-500 py-8">
              ← 点击左侧习语查看详细解释
            </div>
          )}
        </Card>
      </div>
    </div>
  );
};

export default Culture;
