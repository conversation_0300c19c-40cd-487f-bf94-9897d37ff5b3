
-- 为用户的生词本创建一个新表
CREATE TABLE public.vocabulary (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  word TEXT NOT NULL,
  context TEXT,
  definition JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- 在 user_id 和 word 列上创建唯一索引，以防止重复添加并提高查询速度
CREATE UNIQUE INDEX vocabulary_user_id_word_idx ON public.vocabulary (user_id, lower(word));

-- 启用行级安全策略 (RLS)
ALTER TABLE public.vocabulary ENABLE ROW LEVEL SECURITY;

-- 创建策略，允许用户管理自己的生词
CREATE POLICY "Users can manage their own vocabulary"
ON public.vocabulary
FOR ALL
USING (user_id = auth.uid());
