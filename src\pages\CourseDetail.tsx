import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { BookOpen, ArrowLeft, Clock, Users, Star, ChevronLeft, ChevronRight } from 'lucide-react';

const courseDetails: Record<string, {
  title: string;
  image: string;
  level: string;
  duration: string;
  students: number;
  rating: number;
  description: string;
  outline: { title: string; content: string }[];
  lessonContent: { // 新增：详细课程内容
    section: string;    // 章节标题
    content: string;    // 具体内容讲解
    image?: string;     // 可选章节插图
    tips?: string;      // 提示或知识点
    example?: string;   // 例句
  }[];
}> = {
  "1": {
    title: "英语基础入门",
    image: "https://images.unsplash.com/photo-1434030216411-0b793f4b4173?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    level: "初级",
    duration: "4周",
    students: 1200,
    rating: 4.8,
    description: "从零基础开始英语学习，涵盖字母发音、日常词汇、常用句型与基础语法，帮助你迈出英语学习第一步。",
    outline: [
      { title: "第一章：英语字母与发音", content: "学习26个英语字母，掌握国际音标，带领你从“Hello”开口说英语。" },
      { title: "第二章：常用打招呼表达", content: "熟悉日常问候与自我介绍，练习互动情景对话。" },
      { title: "第三章：基础词汇", content: "积累关于家庭、学校、交通等主题的高频单词。" },
      { title: "第四章：英语基础句型", content: "掌握常见的肯定/否定/疑问句，提升基本表达能力。" },
      { title: "第五章：入门语法", content: "学习be动词用法、简单时态，理解句子基本结构。" }
    ],
    lessonContent: [
      {
        section: "第一章：英语字母与发音",
        content: "英语共有26个字母，分为大写和小写。发音要点：A[a] B[bi:] C[si:] ... Z[zi:]. 建议跟着音频多练习发音。常用单词如：apple, book, cat。",
        tips: "记住ABCDE五个元音，常用在英语单词拼写中。",
        example: "字母发音练习：A for 'apple', B for 'book', C for 'cat'."
      },
      {
        section: "第二章：常用打招呼表达",
        content: "英语自我介绍和打招呼：Hi, Hello, How are you? I'm Tom. Nice to meet you! 重复模仿这些短句可以提升交流自信。",
        tips: "每天试着用英语和朋友/同学打1次招呼。",
        example: "'Hello! My name is Mary. How are you?'"
      },
      {
        section: "第三章：基础词汇",
        content: "积累家庭、学校、交通等主题高频词：family, mother, father, school, bus, train, teacher, student。",
        tips: "背单词可以用卡片法，每天10个单词，坚持一周会有明显提升。",
        example: "This is my family. My father is a teacher. I go to school by bus."
      },
      {
        section: "第四章：英语基础句型",
        content: "学习肯定、否定和疑问句：I am a student. / I am not a student. / Am I a student? 尝试替换主语和谓语进行练习。",
        tips: "肯定词 am/is/are 搭配正确的人称；疑问句注意主谓倒装。",
        example: "I am happy. She is not my sister. Are you ready?"
      },
      {
        section: "第五章：入门语法",
        content: "be动词有am/is/are，对应不同主语。简单时态如一般现在时：I play. She plays. 句子结构=主语+谓语+宾语。",
        tips: "口头多造句，熟能生巧。",
        example: "I am a boy. You are a girl. He is a doctor."
      }
    ]
  },
  "2": {
    title: "商务英语进阶",
    image: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    level: "中级",
    duration: "6周",
    students: 800,
    rating: 4.9,
    description: "专为职场人士打造，提升商务写作、邮件、会议和谈判英语能力，助力职业晋升。",
    outline: [
      { title: "第一章：商务沟通基础", content: "邮件、电话和会议常用表达，了解职场用语礼仪。" },
      { title: "第二章：高效写作与回复", content: "学习撰写清晰简洁的邮件、报告等文本，掌握常见句型和格式。" },
      { title: "第三章：会议与讨论", content: "如何发言、表达观点和协调工作，提升会议沟通效率。" },
      { title: "第四章：跨文化谈判", content: "掌握实用的谈判技巧及跨文化交流注意事项。" }
    ],
    lessonContent: [
      {
        section: "第一章：商务沟通基础",
        content: "掌握邮件书写格式（称呼、正文、结尾），电话开场与结束常用句型。注意语气得体，保持专业。",
        tips: "收发邮件前检查语法和礼貌用语。",
        example: "Email开头：Dear Mr. Smith,... 电话表达：Hello, this is Jenny from Marketing."
      },
      {
        section: "第二章：高效写作与回复",
        content: "简明扼要表达主题，分点列举，使用关键词如：attach, schedule, confirm。回复邮件时要引用关键信息。",
        tips: "少用缩写，多用完整表达。",
        example: "Please find the report attached. I am writing to confirm our meeting at 10am."
      },
      {
        section: "第三章：会议与讨论",
        content: "会议中主动发言：I'd like to share my opinion...、Could you clarify...? 善用短句表达和提问。",
        tips: "提前准备发言内容，提升自信。",
        example: "May I add something? I agree with your point."
      },
      {
        section: "第四章：跨文化谈判",
        content: "熟悉不同文化习惯和表达差异。Negotiation: 撒诱饵、让步。常用句: Let's find a solution that works for both.",
        tips: "表达灵活，注意肢体语言和语调。",
        example: "I understand your concern. Let's discuss alternatives."
      }
    ]
  },
  "3": {
    title: "雅思托福冲刺",
    image: "https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    level: "高级",
    duration: "8周",
    students: 600,
    rating: 4.7,
    description: "专项训练听力、口语、写作、阅读四大板块，搭配高频真题，助你考高分。",
    outline: [
      { title: "第一章：写作方法与实战", content: "分析写作评分标准，提升思路与语言组织能力。" },
      { title: "第二章：口语高分表达", content: "多场景模拟题练习，并剖析满分口语答案。" },
      { title: "第三章：听力速记与应试技巧", content: "熟悉题型，强化抓取信息和干扰排除能力。" },
      { title: "第四章：阅读理解精读", content: "解题策略讲解，真实长难句分析与练习。" }
    ],
    lessonContent: [
      {
        section: "第一章：写作方法与实战",
        content: "了解雅思、托福写作评分标准。列出逻辑清晰、结构分明的提纲并多实践批改。",
        example: "A good essay has an introduction, body, and conclusion."
      },
      {
        section: "第二章：口语高分表达",
        content: "多场景模拟题练习，并剖析满分口语答案。",
        example: "Example: I'm sorry, I don't understand. Can you please repeat that?"
      },
      {
        section: "第三章：听力速记与应试技巧",
        content: "熟悉题型，强化抓取信息和干扰排除能力。",
        example: "Example: What is the main idea of the passage?"
      },
      {
        section: "第四章：阅读理解精读",
        content: "解题策略讲解，真实长难句分析与练习。",
        example: "Example: What is the author's opinion on the topic?"
      }
    ]
  },
  "4": {
    title: "日常英语对话",
    image: "https://images.unsplash.com/photo-1573164713714-d95e436ab8d6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    level: "中级",
    duration: "5周",
    students: 950,
    rating: 4.6,
    description: "高频生活场景会话训练，包含购物、点餐、出行、求助等互动，助你开口自如。",
    outline: [
      { title: "第一章：购物与用餐", content: "超市、餐厅、外卖点餐等实用表达。" },
      { title: "第二章：乘车与导航", content: "交通咨询、路线询问和票务办理范例。" },
      { title: "第三章：应急与求助", content: "紧急电话、医生交流等关键用语。" },
      { title: "第四章：休闲与社交", content: "咖啡馆、聚会、休闲娱乐英文交流。" }
    ],
    lessonContent: [
      {
        section: "第一章：购物与用餐",
        content: "超市、餐厅、外卖点餐等实用表达。",
        example: "Example: Can I have a coffee please?"
      },
      {
        section: "第二章：乘车与导航",
        content: "交通咨询、路线询问和票务办理范例。",
        example: "Example: How can I get to the airport?"
      },
      {
        section: "第三章：应急与求助",
        content: "紧急电话、医生交流等关键用语。",
        example: "Example: I'm feeling unwell. Can you call an ambulance?"
      },
      {
        section: "第四章：休闲与社交",
        content: "咖啡馆、聚会、休闲娱乐英文交流。",
        example: "Example: I'd like to go to the park. Do you want to join me?"
      }
    ]
  },
  "5": {
    title: "英语语法精讲",
    image: "https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    level: "初级-中级",
    duration: "6周",
    students: 1100,
    rating: 4.8,
    description: "系统学习英语语法，覆盖时态、主被动、非谓语等难点，提升句型灵活使用能力。",
    outline: [
      { title: "第一章：英语时态系统", content: "现在、过去、将来与完成时详细解析。" },
      { title: "第二章：被动语态与虚拟语气", content: "核心用法拆解与应用练习。" },
      { title: "第三章：从句与非谓语动词", content: "定语/状语/名词性从句，动名词和不定式。" }
    ],
    lessonContent: [
      {
        section: "第一章：英语时态系统",
        content: "现在、过去、将来与完成时详细解析。",
        example: "Example: I went to the store yesterday. She is going to the park tomorrow."
      },
      {
        section: "第二章：被动语态与虚拟语气",
        content: "核心用法拆解与应用练习。",
        example: "Example: It is necessary to study hard. I would like to go to the party."
      },
      {
        section: "第三章：从句与非谓语动词",
        content: "定语/状语/名词性从句，动名词和不定式。",
        example: "Example: The book is on the table. I am reading the book."
      }
    ]
  },
  "6": {
    title: "英语听力提升",
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    level: "中级-高级",
    duration: "4周",
    students: 700,
    rating: 4.9,
    description: "丰富真实语料听力练习，涵盖新闻、访谈、电影片段等，增强理解与听辨能力。",
    outline: [
      { title: "第一章：日常生活场景听力", content: "超市、邮局、银行等场景音频训练。" },
      { title: "第二章：新闻与访谈练习", content: "精选英文新闻和嘉宾访谈听力材料。" },
      { title: "第三章：影视剧片段解析", content: "分析人物台词、情景语气。" }
    ],
    lessonContent: [
      {
        section: "第一章：日常生活场景听力",
        content: "超市、邮局、银行等场景音频训练。",
        example: "Example: What is the weather like today?"
      },
      {
        section: "第二章：新闻与访谈练习",
        content: "精选英文新闻和嘉宾访谈听力材料。",
        example: "Example: What is the main topic of the news?"
      },
      {
        section: "第三章：影视剧片段解析",
        content: "分析人物台词、情景语气。",
        example: "Example: What is the character's motivation in the movie?"
      }
    ]
  },
  "7": {
    title: "口语表达实战",
    image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80",
    level: "初级-高级",
    duration: "5周",
    students: 850,
    rating: 4.9,
    description: "通过角色扮演、模拟演练等进行强化口语表达，提升英语自信心。",
    outline: [
      { title: "第一章：自我介绍与兴趣", content: "多样自我介绍模板，表达个人爱好。" },
      { title: "第二章：问路与求助情景", content: "模拟真实生活中请求帮助和给建议。" },
      { title: "第三章：交流观点", content: "友好争论、表达个人看法和辩论基础。" }
    ],
    lessonContent: [
      {
        section: "第一章：自我介绍与兴趣",
        content: "多样自我介绍模板，表达个人爱好。",
        example: "Example: Hi, my name is John. I love to travel."
      },
      {
        section: "第二章：问路与求助情景",
        content: "模拟真实生活中请求帮助和给建议。",
        example: "Example: Can you help me find the nearest hospital?"
      },
      {
        section: "第三章：交流观点",
        content: "友好争论、表达个人看法和辩论基础。",
        example: "Example: I think we should go to the park. What do you think?"
      }
    ]
  },
  "8": {
    title: "英美文化体验课",
    image: "https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80",
    level: "中级",
    duration: "4周",
    students: 600,
    rating: 4.7,
    description: "通过影视、文学、节日等课程内容深入体验英语国家文化，增进跨文化感知。",
    outline: [
      { title: "第一章：影视剧与流行文化", content: "美剧、英剧与音乐作品赏析，提升语言兴趣。" },
      { title: "第二章：文学与历史故事", content: "英美著名作家作品、经典历史事件解读。" },
      { title: "第三章：传统节日与习俗", content: "万圣节、圣诞节等传统文化探秘。" }
    ],
    lessonContent: [
      {
        section: "第一章：影视剧与流行文化",
        content: "美剧、英剧与音乐作品赏析，提升语言兴趣。",
        example: "Example: What is your favorite movie?"
      },
      {
        section: "第二章：文学与历史故事",
        content: "英美著名作家作品、经典历史事件解读。",
        example: "Example: What is the significance of the American Revolution?"
      },
      {
        section: "第三章：传统节日与习俗",
        content: "万圣节、圣诞节等传统文化探秘。",
        example: "Example: What is the origin of Halloween?"
      }
    ]
  },
  "9": {
    title: "英语写作与改进",
    image: "https://images.unsplash.com/photo-1487058792275-0ad4aaf24ca7?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80",
    level: "中级-高级",
    duration: "7周",
    students: 720,
    rating: 4.85,
    description: "分步提升写作结构、词汇表达和语法正确率，适合留学和职场写作场景。",
    outline: [
      { title: "第一章：写作结构与开篇", content: "掌握文章起承转合与布局。" },
      { title: "第二章：常见错误与润色", content: "识别并改正写作中常见语法、用词、逻辑问题。" },
      { title: "第三章：高阶表达", content: "练习复杂句型与多样词汇，提高表达力。" }
    ],
    lessonContent: [
      {
        section: "第一章：写作结构与开篇",
        content: "掌握文章起承转合与布局。",
        example: "Example: The first paragraph introduces the topic. The second paragraph provides evidence. The third paragraph summarizes the main points."
      },
      {
        section: "第二章：常见错误与润色",
        content: "识别并改正写作中常见语法、用词、逻辑问题。",
        example: "Example: The sentence is grammatically correct, but it could be more concise."
      },
      {
        section: "第三章：高阶表达",
        content: "练习复杂句型与多样词汇，提高表达力。",
        example: "Example: The company's new policy will have a significant impact on the employees' work."
      }
    ]
  },
};

const CourseDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const course = courseDetails[id || ""];

  const navigate = useNavigate();
  const [showStudy, setShowStudy] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);

  if (!course) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center text-center bg-gray-50">
        <div className="rounded-xl bg-white shadow p-8">
          <p className="text-lg mb-6">未找到该课程，请返回课程列表。</p>
          <button
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            onClick={() => navigate("/courses")}
          >
            返回课程列表
          </button>
        </div>
      </div>
    );
  }

  const hasLesson = course.lessonContent && course.lessonContent.length > 0;
  const totalStep = hasLesson ? course.lessonContent.length : 0;

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-3xl mx-auto px-4">
        <button
          onClick={() => navigate("/courses")}
          className="flex items-center mb-6 text-blue-600 hover:underline hover:gap-2 transition-all"
        >
          <ArrowLeft className="mr-1" size={20} />
          返回课程列表
        </button>

        <div className="bg-white rounded-2xl shadow p-6 md:flex gap-6 mb-8 animate-fade-in">
          <img
            src={course.image}
            alt={course.title}
            className="w-36 h-36 rounded-xl object-cover mb-4 md:mb-0"
          />
          <div>
            <h1 className="text-2xl md:text-3xl font-bold mb-2">{course.title}</h1>
            <p className="mb-3 text-gray-600">{course.description}</p>
            <div className="flex flex-wrap gap-4 text-gray-500 text-base">
              <span className="flex items-center"><BookOpen size={18} className="mr-1" />{course.level}</span>
              <span className="flex items-center"><Clock size={18} className="mr-1" />{course.duration}</span>
              <span className="flex items-center"><Users size={18} className="mr-1" />{course.students}人</span>
              <span className="flex items-center"><Star size={18} className="mr-1 text-yellow-500" />{course.rating}</span>
            </div>
          </div>
        </div>

        {/* 课程大纲 */}
        <div className="bg-white rounded-2xl shadow p-6 animate-fade-in mb-8">
          <h2 className="text-xl font-bold mb-4 flex items-center"><BookOpen className="mr-2" />课程大纲</h2>
          <ul className="divide-y">
            {course.outline.map((item, idx) => (
              <li key={idx} className="py-4">
                <div className="font-semibold text-base mb-1">{item.title}</div>
                <div className="text-gray-600">{item.content}</div>
              </li>
            ))}
          </ul>
        </div>

        {/* 互动学习模块 */}
        <div className="bg-white rounded-2xl shadow p-6 mb-8 animate-fade-in">
          <h2 className="text-xl font-bold mb-4 flex items-center">
            <BookOpen className="mr-2" />
            互动学习
          </h2>
          {!showStudy ? (
            <button
              className="bg-gradient-to-r from-blue-500 to-emerald-600 text-white px-8 py-3 rounded-xl font-semibold text-lg shadow hover:scale-105 transition"
              disabled={!hasLesson}
              onClick={() => setShowStudy(true)}
            >
              {hasLesson ? "开始学习" : "课程待完善"}
            </button>
          ) : hasLesson && (
            <div className="flex flex-col items-center gap-6">
              {/* 章节索引 */}
              <div className="text-base text-gray-500">
                第 {currentStep + 1} / {totalStep} 节：{course.lessonContent[currentStep].section}
              </div>
              {/* 章节内容 */}
              <div className="w-full break-words bg-blue-50 rounded-xl px-6 py-5 text-gray-900">
                <div className="text-lg font-semibold mb-2">{course.lessonContent[currentStep].section}</div>
                <div className="mb-2">{course.lessonContent[currentStep].content}</div>
                {course.lessonContent[currentStep].tips && (
                  <div className="mb-2 text-blue-600">小贴士：{course.lessonContent[currentStep].tips}</div>
                )}
                {course.lessonContent[currentStep].example && (
                  <div className="mb-2 text-emerald-700 bg-emerald-50 px-3 py-2 rounded">
                    例句：{course.lessonContent[currentStep].example}
                  </div>
                )}
                {course.lessonContent[currentStep].image && (
                  <img src={course.lessonContent[currentStep].image} className="rounded-xl mt-2" />
                )}
              </div>
              {/* 分节切换按钮 */}
              <div className="flex items-center gap-4">
                <button
                  className="bg-gray-100 px-6 py-2 rounded-lg flex items-center disabled:opacity-50"
                  onClick={() => setCurrentStep((c) => Math.max(0, c - 1))}
                  disabled={currentStep === 0}
                >
                  <ChevronLeft size={20} /> 上一节
                </button>
                <button
                  className="bg-blue-600 text-white px-6 py-2 rounded-lg flex items-center disabled:opacity-50"
                  onClick={() => setCurrentStep((c) => Math.min(totalStep - 1, c + 1))}
                  disabled={currentStep === totalStep - 1}
                >
                  下一节 <ChevronRight size={20} />
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CourseDetail;
