import React, { useEffect } from 'react';
import ExerciseSelector from '@/components/practice/ExerciseSelector';
import AudioUploader from '@/components/practice/AudioUploader';
import AudioPlayer from '@/components/practice/AudioPlayer';
import QuestionsList from '@/components/practice/QuestionsList';
import ResultsDisplay from '@/components/practice/ResultsDisplay';
import PracticeModeSelector from '@/components/practice/PracticeModeSelector';
import DictationMode from '@/components/practice/DictationMode';
import FillBlankMode from '@/components/practice/FillBlankMode';
import SpeakingMode from '@/components/practice/SpeakingMode';
import VocabularyMarker from '@/components/practice/VocabularyMarker';
import { usePracticeState } from '@/hooks/usePracticeState';
import { useAudioHandlers } from '@/hooks/useAudioHandlers';
import { usePracticeHandlers } from '@/hooks/usePracticeHandlers';
import { exercises } from '@/data/practiceExercises';
import { useLocation } from 'react-router-dom';

const Practice = () => {
  const {
    currentExercise,
    isPlaying,
    currentTime,
    duration,
    showTranscript,
    answers,
    showResults,
    isUploading,
    isGenerating,
    audioFiles,
    playbackRate,
    isLooping,
    loopStart,
    loopEnd,
    practiceMode,
    vocabulary,
    audioRef,
    setCurrentExercise,
    setIsPlaying,
    setCurrentTime,
    setDuration,
    setShowTranscript,
    setAnswers,
    setShowResults,
    setIsUploading,
    setIsGenerating,
    setAudioFiles,
    setPlaybackRate,
    setIsLooping,
    setLoopStart,
    setLoopEnd,
    setPracticeMode,
    setVocabulary
  } = usePracticeState();

  const location = useLocation();

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const mode = params.get('mode');
    if (mode === 'listening' || mode === 'dictation' || mode === 'fillBlank' || mode === 'speaking') {
      setPracticeMode(mode);
    }
  }, [location.search, setPracticeMode]);

  const {
    hasAudioForCurrentExercise,
    handleAudioUpload,
    handleGenerateAudio,
    togglePlayPause,
    resetAudio,
    handlePlaybackRateChange,
    handleSetLoopStart,
    handleSetLoopEnd,
    handleClearLoop,
    handleRemoveAudio,
    handleToggleLoop
  } = useAudioHandlers({
    audioRef,
    isPlaying,
    setIsPlaying,
    setCurrentTime,
    setDuration,
    isLooping,
    loopStart,
    loopEnd,
    currentExercise,
    setPlaybackRate,
    setLoopStart,
    setLoopEnd,
    audioFiles,
    playbackRate,
    setIsUploading,
    isGenerating,
    setIsGenerating,
    setAudioFiles,
    setIsLooping
  });

  const {
    handleExerciseSelect,
    handleAnswerSelect,
    checkAnswers,
    retryExercise,
    nextExercise,
    handleModeChange,
    handleDictationComplete,
    handleFillBlankComplete,
    handleSpeakingComplete,
    handleVocabularyUpdate
  } = usePracticeHandlers({
    setCurrentExercise,
    setAnswers,
    setShowResults,
    setShowTranscript,
    setIsPlaying,
    setCurrentTime,
    setPracticeMode,
    setVocabulary,
    currentExercise,
    totalExercises: exercises.length
  });

  const currentEx = exercises[currentExercise];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-emerald-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <audio ref={audioRef} />
        
        <ExerciseSelector
          exercises={exercises}
          currentExercise={currentExercise}
          onExerciseSelect={handleExerciseSelect}
        />

        <div className="bg-white rounded-2xl shadow-lg mb-6 p-6">
          <AudioUploader
            onAudioUpload={handleAudioUpload}
            onRemoveAudio={handleRemoveAudio}
            hasAudio={hasAudioForCurrentExercise}
            isUploading={isUploading}
            onGenerateAudio={() => handleGenerateAudio(currentEx.transcript)}
            isGenerating={isGenerating}
          />
        </div>

        {practiceMode !== 'dictation' && practiceMode !== 'fillBlank' && (
          <AudioPlayer
            title={currentEx.title}
            transcript={currentEx.transcript}
            isPlaying={isPlaying}
            currentTime={currentTime}
            duration={duration}
            showTranscript={showTranscript}
            hasAudio={hasAudioForCurrentExercise}
            playbackRate={playbackRate}
            isLooping={isLooping}
            loopStart={loopStart}
            loopEnd={loopEnd}
            onPlayPause={togglePlayPause}
            onReset={resetAudio}
            onToggleTranscript={() => setShowTranscript(!showTranscript)}
            onPlaybackRateChange={handlePlaybackRateChange}
            onToggleLoop={handleToggleLoop}
            onSetLoopStart={() => handleSetLoopStart(currentTime)}
            onSetLoopEnd={() => handleSetLoopEnd(currentTime)}
            onClearLoop={handleClearLoop}
          />
        )}

        <PracticeModeSelector
          currentMode={practiceMode}
          onModeChange={handleModeChange}
        />

        {practiceMode === 'listening' && (
          <div className="bg-white rounded-2xl shadow-lg p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-6">理解测试</h3>
            
            <QuestionsList
              questions={currentEx.questions}
              answers={answers}
              showResults={showResults}
              onAnswerSelect={handleAnswerSelect}
            />

            <ResultsDisplay
              questions={currentEx.questions}
              answers={answers}
              showResults={showResults}
              currentExercise={currentExercise}
              totalExercises={exercises.length}
              onCheckAnswers={checkAnswers}
              onRetry={retryExercise}
              onNextExercise={nextExercise}
            />
          </div>
        )}

        {practiceMode === 'dictation' && (
          <DictationMode
            transcript={currentEx.transcript}
            onComplete={handleDictationComplete}
            onReset={() => {}}
          />
        )}

        {practiceMode === 'fillBlank' && (
          <FillBlankMode
            transcript={currentEx.transcript}
            onComplete={handleFillBlankComplete}
            onReset={() => {}}
          />
        )}

        {practiceMode === 'speaking' && (
          <SpeakingMode
            transcript={currentEx.transcript}
            audioUrl={hasAudioForCurrentExercise ? audioFiles[currentExercise] : undefined}
            onComplete={handleSpeakingComplete}
            onReset={() => {}}
          />
        )}

        <VocabularyMarker
          transcript={currentEx.transcript}
          onVocabularyUpdate={handleVocabularyUpdate}
        />

        {vocabulary.length > 0 && (
          <div className="bg-white rounded-2xl shadow-lg p-6 mt-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">当前词汇本摘要</h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-sm text-gray-600 mb-2">已标记 {vocabulary.length} 个词汇</p>
              <div className="text-sm text-gray-800">
                {vocabulary.slice(0, 3).map((word, index) => (
                  <div key={index}>{word}</div>
                ))}
                {vocabulary.length > 3 && (
                  <div className="text-gray-500 mt-1">...还有 {vocabulary.length - 3} 个词汇</div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Practice;
