
import React from 'react';
import { useLocation } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { MessageCircle, Sparkles } from 'lucide-react';
import AIChat from '@/components/AIChat';

const FloatingAIChat = () => {
  const location = useLocation();

  // Hide the button on the main chat page to avoid redundancy
  if (location.pathname === '/chat') {
    return null;
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          className="fixed bottom-6 right-6 h-16 w-16 rounded-full bg-gradient-to-r from-blue-600 to-emerald-600 text-white shadow-2xl hover:scale-110 transition-transform duration-300 z-50 flex items-center justify-center"
          aria-label="Open AI Chat"
        >
          <MessageCircle size={32} />
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl w-full h-[90vh] flex flex-col p-0 sm:max-w-2xl">
        <DialogHeader className="p-6 pb-2 border-b">
          <DialogTitle className="flex items-center gap-2 text-lg">
            <Sparkles className="w-5 h-5 text-emerald-500" />
            AI 英语助手
          </DialogTitle>
        </DialogHeader>
        <div className="flex-1 overflow-hidden">
            <AIChat />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default FloatingAIChat;
