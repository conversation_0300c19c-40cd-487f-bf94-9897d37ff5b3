import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { 
  Settings, 
  Save, 
  TestTube,
  CheckCircle,
  XCircle,
  Eye,
  EyeOff
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { localAIService, AIConfig } from '@/services/localAIService';
import { localAudioService, AudioGenerationConfig } from '@/services/localAudioService';
import { alicloudService } from '@/services/alicloudService';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const ConfigManager = () => {
  const { toast } = useToast();
  const [showApiKeys, setShowApiKeys] = useState(false);
  const [testing, setTesting] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<{
    ai: boolean | null;
    audio: boolean | null;
  }>({ ai: null, audio: null });

  // AI 配置
  const [aiConfig, setAiConfig] = useState<AIConfig>({
    provider: 'alicloud',
    apiKey: '',
    model: 'qwen-turbo',
    imageModel: 'wanx2.1-t2i-turbo', // 添加图像模型配置
    groupId: '',
    baseUrl: ''
  });

  // 音频配置
  const [audioConfig, setAudioConfig] = useState<AudioGenerationConfig>({
    provider: 'browser',
    apiKey: '',
    groupId: '',
    voiceId: 'Cherry',
    speed: 1,
    volume: 1,
    pitch: 0
  });

  // 加载保存的配置
  useEffect(() => {
    const savedAiConfig = localStorage.getItem('ai_config');
    const savedAudioConfig = localStorage.getItem('audio_config');

    if (savedAiConfig) {
      const config = JSON.parse(savedAiConfig);
      setAiConfig(config);
      localAIService.setConfig(config);
    }

    if (savedAudioConfig) {
      const config = JSON.parse(savedAudioConfig);
      setAudioConfig(config);
      localAudioService.setConfig(config);
    }
  }, []);

  // 保存配置
  const saveConfig = () => {
    try {
      localStorage.setItem('ai_config', JSON.stringify(aiConfig));
      localStorage.setItem('audio_config', JSON.stringify(audioConfig));

      // 配置本地AI服务
      localAIService.setConfig(aiConfig);
      localAudioService.setConfig(audioConfig);

      // 配置阿里云服务
      if (aiConfig.provider === 'alicloud' && aiConfig.apiKey) {
        alicloudService.setConfig({
          apiKey: aiConfig.apiKey
        });
      }

      toast({
        title: "配置已保存",
        description: "API 配置已成功保存到本地。",
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "保存失败",
        description: "配置保存时出现错误。",
      });
    }
  };

  // 测试连接
  const testConnections = async () => {
    setTesting(true);
    setConnectionStatus({ ai: null, audio: null });

    try {
      // 先配置服务，再测试连接
      localAIService.setConfig(aiConfig);
      localAudioService.setConfig(audioConfig);

      // 配置阿里云服务
      if (aiConfig.provider === 'alicloud' && aiConfig.apiKey) {
        alicloudService.setConfig({
          apiKey: aiConfig.apiKey
        });
      }

      // 测试 AI 连接
      const aiResult = await localAIService.testConnection();
      setConnectionStatus(prev => ({ ...prev, ai: aiResult }));

      // 测试音频连接
      const audioResult = await localAudioService.testConnection();
      setConnectionStatus(prev => ({ ...prev, audio: audioResult }));

      if (aiResult && audioResult) {
        toast({
          title: "连接测试成功",
          description: "所有服务连接正常。",
        });
      } else {
        toast({
          variant: "destructive",
          title: "连接测试失败",
          description: "部分服务连接失败，请检查配置。",
        });
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "测试失败",
        description: "连接测试时出现错误。",
      });
    } finally {
      setTesting(false);
    }
  };

  // 获取模型选项
  const getModelOptions = () => {
    switch (aiConfig.provider) {
      case 'alicloud':
        return ['qwen-turbo', 'qwen-plus', 'qwen-max', 'qwen-max-longcontext', 'qwen2.5-omni-7b'];
      case 'minimax':
        return ['abab6.5s-chat', 'abab6.5g-chat', 'abab5.5s-chat'];
      case 'siliconflow':
        return ['Qwen/Qwen2.5-7B-Instruct', 'Qwen/Qwen2.5-14B-Instruct', 'meta-llama/Meta-Llama-3.1-8B-Instruct'];
      case 'openai':
        return ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo'];
      case 'ollama':
        return ['llama2', 'llama3', 'qwen2', 'gemma'];
      default:
        return [];
    }
  };

  // 获取图像模型选项
  const getImageModelOptions = () => {
    switch (aiConfig.provider) {
      case 'alicloud':
        return [
          { id: 'wanx2.1-t2i-turbo', name: '通义万相V2.1-Turbo (推荐)', price: '0.14元/张' },
          { id: 'wanx2.1-t2i-plus', name: '通义万相V2.1-Plus', price: '0.20元/张' },
          { id: 'wanx2.0-t2i-turbo', name: '通义万相V2.0-Turbo', price: '0.04元/张' },
          { id: 'flux-dev', name: 'FLUX Dev', price: '较高' },
          { id: 'flux-schnell', name: 'FLUX Schnell', price: '中等' },
          { id: 'wanx-v1', name: '通义万相 V1', price: '0.16元/张' }
        ];
      default:
        return [];
    }
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Settings className="mr-2 h-4 w-4" />
          API 配置
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Settings className="mr-2 h-5 w-5" />
            本地 AI 服务配置
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* AI 服务配置 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center justify-between">
                AI 服务配置
                {connectionStatus.ai !== null && (
                  connectionStatus.ai ? (
                    <Badge variant="default" className="bg-green-500">
                      <CheckCircle className="mr-1 h-3 w-3" />
                      已连接
                    </Badge>
                  ) : (
                    <Badge variant="destructive">
                      <XCircle className="mr-1 h-3 w-3" />
                      连接失败
                    </Badge>
                  )
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="ai-provider">AI 提供商</Label>
                <Select 
                  value={aiConfig.provider} 
                  onValueChange={(value: any) => setAiConfig(prev => ({ ...prev, provider: value, model: '' }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择 AI 提供商" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="alicloud">阿里云 (推荐)</SelectItem>
                    <SelectItem value="minimax">MiniMax</SelectItem>
                    <SelectItem value="siliconflow">SiliconFlow</SelectItem>
                    <SelectItem value="openai">OpenAI</SelectItem>
                    <SelectItem value="ollama">Ollama (本地)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {aiConfig.provider !== 'ollama' && (
                <div>
                  <Label htmlFor="ai-api-key">API Key</Label>
                  <div className="flex space-x-2">
                    <Input
                      id="ai-api-key"
                      type={showApiKeys ? "text" : "password"}
                      value={aiConfig.apiKey}
                      onChange={(e) => setAiConfig(prev => ({ ...prev, apiKey: e.target.value }))}
                      placeholder="输入 API Key"
                    />
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => setShowApiKeys(!showApiKeys)}
                    >
                      {showApiKeys ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
              )}

              {aiConfig.provider === 'minimax' && (
                <div>
                  <Label htmlFor="ai-group-id">Group ID</Label>
                  <Input
                    id="ai-group-id"
                    value={aiConfig.groupId}
                    onChange={(e) => setAiConfig(prev => ({ ...prev, groupId: e.target.value }))}
                    placeholder="输入 MiniMax Group ID"
                  />
                </div>
              )}



              {aiConfig.provider === 'ollama' && (
                <div>
                  <Label htmlFor="ai-base-url">Base URL</Label>
                  <Input
                    id="ai-base-url"
                    value={aiConfig.baseUrl}
                    onChange={(e) => setAiConfig(prev => ({ ...prev, baseUrl: e.target.value }))}
                    placeholder="http://localhost:11434"
                  />
                </div>
              )}

              <div>
                <Label htmlFor="ai-model">文本模型</Label>
                <Select
                  value={aiConfig.model}
                  onValueChange={(value) => setAiConfig(prev => ({ ...prev, model: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择文本模型" />
                  </SelectTrigger>
                  <SelectContent>
                    {getModelOptions().map(model => (
                      <SelectItem key={model} value={model}>{model}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {aiConfig.provider === 'alicloud' && (
                <div>
                  <Label htmlFor="ai-image-model">图像模型</Label>
                  <Select
                    value={aiConfig.imageModel || 'wanx2.1-t2i-turbo'}
                    onValueChange={(value) => setAiConfig(prev => ({ ...prev, imageModel: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择图像模型" />
                    </SelectTrigger>
                    <SelectContent>
                      {getImageModelOptions().map(model => (
                        <SelectItem key={model.id} value={model.id}>
                          <div className="flex flex-col">
                            <span>{model.name}</span>
                            <span className="text-xs text-gray-500">{model.price}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-gray-500 mt-1">
                    推荐使用 V2.1-Turbo：速度快、质量高、成本低
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 音频服务配置 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center justify-between">
                音频服务配置
                {connectionStatus.audio !== null && (
                  connectionStatus.audio ? (
                    <Badge variant="default" className="bg-green-500">
                      <CheckCircle className="mr-1 h-3 w-3" />
                      已连接
                    </Badge>
                  ) : (
                    <Badge variant="destructive">
                      <XCircle className="mr-1 h-3 w-3" />
                      连接失败
                    </Badge>
                  )
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="audio-provider">音频提供商</Label>
                <Select
                  value={audioConfig.provider}
                  onValueChange={(value: any) => setAudioConfig(prev => ({ ...prev, provider: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择音频提供商" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="alicloud">阿里云 Qwen-TTS (推荐)</SelectItem>
                    <SelectItem value="minimax">MiniMax</SelectItem>
                    <SelectItem value="browser">浏览器原生 TTS</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {audioConfig.provider !== 'browser' && (
                <div>
                  <Label htmlFor="audio-api-key">API Key</Label>
                  <div className="flex space-x-2">
                    <Input
                      id="audio-api-key"
                      type={showApiKeys ? "text" : "password"}
                      value={audioConfig.apiKey}
                      onChange={(e) => setAudioConfig(prev => ({ ...prev, apiKey: e.target.value }))}
                      placeholder={`输入 ${audioConfig.provider === 'alicloud' ? '阿里云' : 'MiniMax'} API Key`}
                    />
                  </div>
                </div>
              )}

              {audioConfig.provider === 'minimax' && (
                <div>
                  <Label htmlFor="audio-group-id">Group ID</Label>
                  <Input
                    id="audio-group-id"
                    value={audioConfig.groupId}
                    onChange={(e) => setAudioConfig(prev => ({ ...prev, groupId: e.target.value }))}
                    placeholder="输入 MiniMax Group ID"
                  />
                </div>
              )}

              <div>
                <Label htmlFor="voice-id">语音选择</Label>
                <Select
                  value={audioConfig.voiceId}
                  onValueChange={(value) => setAudioConfig(prev => ({ ...prev, voiceId: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {audioConfig.provider === 'alicloud' ? (
                      <>
                        <SelectItem value="Cherry">Cherry (女声)</SelectItem>
                        <SelectItem value="Serena">Serena (女声)</SelectItem>
                        <SelectItem value="Ethan">Ethan (男声)</SelectItem>
                        <SelectItem value="Chelsie">Chelsie (女声)</SelectItem>
                      </>
                    ) : (
                      <>
                        <SelectItem value="male-qn-qingse">男声-青涩</SelectItem>
                        <SelectItem value="female-shaonv">女声-少女</SelectItem>
                        <SelectItem value="male-qn-jingying">男声-精英</SelectItem>
                        <SelectItem value="female-qn-wenwen">女声-温文</SelectItem>
                      </>
                    )}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="speed">语速: {audioConfig.speed}</Label>
                  <Input
                    id="speed"
                    type="range"
                    min="0.5"
                    max="2"
                    step="0.1"
                    value={audioConfig.speed}
                    onChange={(e) => setAudioConfig(prev => ({ ...prev, speed: parseFloat(e.target.value) }))}
                  />
                </div>
                <div>
                  <Label htmlFor="volume">音量: {audioConfig.volume}</Label>
                  <Input
                    id="volume"
                    type="range"
                    min="0"
                    max="2"
                    step="0.1"
                    value={audioConfig.volume}
                    onChange={(e) => setAudioConfig(prev => ({ ...prev, volume: parseFloat(e.target.value) }))}
                  />
                </div>
                <div>
                  <Label htmlFor="pitch">音调: {audioConfig.pitch}</Label>
                  <Input
                    id="pitch"
                    type="range"
                    min="-1"
                    max="1"
                    step="0.1"
                    value={audioConfig.pitch}
                    onChange={(e) => setAudioConfig(prev => ({ ...prev, pitch: parseFloat(e.target.value) }))}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 操作按钮 */}
          <div className="flex space-x-2">
            <Button onClick={saveConfig} className="flex-1">
              <Save className="mr-2 h-4 w-4" />
              保存配置
            </Button>
            <Button 
              variant="outline" 
              onClick={testConnections}
              disabled={testing}
              className="flex-1"
            >
              <TestTube className="mr-2 h-4 w-4" />
              {testing ? '测试中...' : '测试连接'}
            </Button>
          </div>

          {/* 说明文字 */}
          <div className="text-xs text-gray-500 space-y-1">
            <p>• 配置保存在浏览器本地存储中，不会上传到服务器</p>
            <p>• <strong>阿里云</strong>：推荐使用，支持文本生成、图像生成和语音合成</p>
            <p>• <strong>MiniMax</strong>：需要 API Key 和 Group ID</p>
            <p>• <strong>Ollama</strong>：需要本地运行服务</p>
            <p>• 如果没有配置，将使用浏览器原生 TTS 作为备选</p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ConfigManager;
