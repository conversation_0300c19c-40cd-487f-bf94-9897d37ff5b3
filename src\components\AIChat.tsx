
import React, { useState } from 'react';
import { useAIChat } from '@/hooks/useAIChat';
import VoiceControl from './VoiceControl';
import WordCard from './WordCard';
import ChatHeader from './chat/ChatHeader';
import MessageList from './chat/MessageList';
import MessageInput from './chat/MessageInput';

const AIChat = () => {
  const [inputMessage, setInputMessage] = useState('');
  const [selectedWord, setSelectedWord] = useState<string | null>(null);
  const [selectedWordContext, setSelectedWordContext] = useState<string | null>(null);
  const {
    messages,
    loading,
    isTtsLoading,
    sendMessage,
    clearMessages,
    selectedModel,
    setSelectedModel,
    playTextToSpeech,
    toggleMessagePlayback,
    activeSpeech,
  } = useAIChat();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputMessage.trim() || loading) return;
    
    await sendMessage(inputMessage);
    setInputMessage('');
  };

  const handleVoiceTranscript = async (transcript: string) => {
    if (transcript.trim()) {
      await sendMessage(transcript);
    }
  };

  const handleWordClick = (word: string, context: string) => {
    setSelectedWord(word);
    setSelectedWordContext(context);
  };

  return (
    <div className="flex flex-col h-[85vh] bg-white rounded-lg shadow-lg">
      <ChatHeader 
        selectedModel={selectedModel}
        onModelChange={setSelectedModel}
        onClearMessages={clearMessages}
      />

      <VoiceControl 
        onTranscript={handleVoiceTranscript}
        isProcessing={loading}
      />

      <MessageList 
        messages={messages}
        loading={loading}
        activeSpeech={activeSpeech}
        onWordClick={handleWordClick}
        toggleMessagePlayback={toggleMessagePlayback}
      />
      
      <WordCard
        word={selectedWord || ''}
        context={selectedWordContext || ''}
        isOpen={!!selectedWord}
        onClose={() => {
          setSelectedWord(null);
          setSelectedWordContext(null);
        }}
        playTextToSpeech={playTextToSpeech}
        isTtsLoading={isTtsLoading}
      />

      <MessageInput 
        inputMessage={inputMessage}
        onInputChange={(e) => setInputMessage(e.target.value)}
        onSubmit={handleSubmit}
        loading={loading}
      />
    </div>
  );
};

export default AIChat;
