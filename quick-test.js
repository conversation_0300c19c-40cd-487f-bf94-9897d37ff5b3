// 快速测试阿里云 API - 使用正确的格式
const apiKey = "sk-3207773e69984ecf9ca3d452fa6d4661";

// 测试文本生成 API (OpenAI 兼容格式)
async function testTextGeneration() {
    console.log("🧪 测试文本生成 API...");

    try {
        const response = await fetch('https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`,
            },
            body: JSON.stringify({
                model: 'qwen-turbo',
                messages: [
                    { role: 'user', content: 'Hello, how are you?' }
                ],
                temperature: 0.7,
                max_tokens: 50
            })
        });

        console.log("📡 文本生成响应状态:", response.status);

        if (!response.ok) {
            const errorText = await response.text();
            console.error("❌ 文本生成错误:", errorText);
            return false;
        }

        const result = await response.json();
        console.log("✅ 文本生成成功:", result);
        console.log("📝 生成的文本:", result.choices?.[0]?.message?.content);
        return true;

    } catch (error) {
        console.error("❌ 文本生成失败:", error.message);
        return false;
    }
}

// 测试语音合成 API
async function testTTS() {
    console.log("🎵 测试语音合成 API...");

    try {
        const response = await fetch('https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`,
            },
            body: JSON.stringify({
                model: 'qwen-tts',
                input: {
                    text: 'Hello, this is a test.',
                    voice: 'Cherry'
                }
            })
        });

        console.log("📡 TTS 响应状态:", response.status);

        if (!response.ok) {
            const errorText = await response.text();
            console.error("❌ TTS 错误:", errorText);
            return false;
        }

        const result = await response.json();
        console.log("✅ TTS 成功:", result);
        console.log("🎵 音频 URL:", result.output?.audio?.url);
        return true;

    } catch (error) {
        console.error("❌ TTS 失败:", error.message);
        return false;
    }
}

// 运行所有测试
async function runAllTests() {
    console.log("🚀 开始测试阿里云 API...\n");

    const textResult = await testTextGeneration();
    console.log("\n" + "=".repeat(50) + "\n");

    const ttsResult = await testTTS();
    console.log("\n" + "=".repeat(50) + "\n");

    if (textResult && ttsResult) {
        console.log("🎉 所有 API 测试成功！");
    } else {
        console.log("💔 部分 API 测试失败");
        console.log(`文本生成: ${textResult ? '✅' : '❌'}`);
        console.log(`语音合成: ${ttsResult ? '✅' : '❌'}`);
    }
}

// 运行测试
runAllTests();
