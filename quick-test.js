// 快速测试阿里云 API
const apiKey = "sk-3207773e69984ecf9ca3d452fa6d4661";

async function testAliCloudAPI() {
    console.log("🧪 开始测试阿里云 API...");
    
    try {
        const response = await fetch('https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`,
            },
            body: JSON.stringify({
                model: 'qwen-turbo',
                input: {
                    messages: [
                        { role: 'user', content: 'Hello' }
                    ]
                },
                parameters: {
                    temperature: 0.7,
                    max_tokens: 50
                }
            })
        });

        console.log("📡 响应状态:", response.status);
        
        if (!response.ok) {
            const errorText = await response.text();
            console.error("❌ API 错误:", errorText);
            return false;
        }

        const result = await response.json();
        console.log("✅ API 响应成功:", result);
        console.log("📝 生成的文本:", result.output?.text || result.output?.choices?.[0]?.message?.content);
        return true;
        
    } catch (error) {
        console.error("❌ 请求失败:", error.message);
        return false;
    }
}

// 运行测试
testAliCloudAPI().then(success => {
    if (success) {
        console.log("🎉 阿里云 API 工作正常！");
    } else {
        console.log("💔 阿里云 API 测试失败");
    }
});
