-- 修复故事表的行级安全策略，允许匿名用户创建故事

-- 删除现有的插入策略
DROP POLICY IF EXISTS "Users can create their own stories" ON public.stories;

-- 创建新的插入策略，允许认证用户创建自己的故事，或者允许匿名故事（user_id为null）
CREATE POLICY "Users can create stories" 
  ON public.stories 
  FOR INSERT 
  WITH CHECK (
    -- 如果用户已登录，user_id必须匹配当前用户
    (auth.uid() IS NOT NULL AND auth.uid() = user_id) OR
    -- 或者允许匿名故事（user_id为null）
    (auth.uid() IS NULL AND user_id IS NULL)
  );

-- 更新查看策略，确保所有人都能查看所有故事
DROP POLICY IF EXISTS "Users can view all stories" ON public.stories;
CREATE POLICY "Users can view all stories" 
  ON public.stories 
  FOR SELECT 
  USING (true);

-- 更新更新策略，只允许用户更新自己的故事（不允许更新匿名故事）
DROP POLICY IF EXISTS "Users can update their own stories" ON public.stories;
CREATE POLICY "Users can update their own stories" 
  ON public.stories 
  FOR UPDATE 
  USING (auth.uid() IS NOT NULL AND auth.uid() = user_id);

-- 更新删除策略，只允许用户删除自己的故事（不允许删除匿名故事）
DROP POLICY IF EXISTS "Users can delete their own stories" ON public.stories;
CREATE POLICY "Users can delete their own stories" 
  ON public.stories 
  FOR DELETE 
  USING (auth.uid() IS NOT NULL AND auth.uid() = user_id);
