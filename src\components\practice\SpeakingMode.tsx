import React, { useState, useRef, useEffect } from 'react';
import { Mic, MicOff, Play, Pause, RotateCcw, Volume2, Star, Loader2 } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

interface SpeakingModeProps {
  transcript: string;
  audioUrl?: string;
  onComplete: (recordingUrl: string) => void;
  onReset: () => void;
}

const SpeakingMode = ({ transcript, audioUrl, onComplete, onReset }: SpeakingModeProps) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isPlayingOriginal, setIsPlayingOriginal] = useState(false);
  const [isPlayingRecording, setIsPlayingRecording] = useState(false);
  const [recordingUrl, setRecordingUrl] = useState<string | null>(null);
  const [hasRecording, setHasRecording] = useState(false);
  const [isEvaluating, setIsEvaluating] = useState(false);
  const [evaluationResult, setEvaluationResult] = useState<{ score: number; feedback: string; userTranscript: string; } | null>(null);
  
  const { toast } = useToast();
  
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const originalAudioRef = useRef<HTMLAudioElement>(null);
  const recordingAudioRef = useRef<HTMLAudioElement>(null);
  const chunksRef = useRef<Blob[]>([]);

  const blobToBase64 = (blob: Blob): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64data = reader.result as string;
        resolve(base64data.split(',')[1]);
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  };

  const evaluateRecording = async (blob: Blob) => {
    setIsEvaluating(true);
    setEvaluationResult(null);
    try {
      const audioBase64 = await blobToBase64(blob);
      const { data, error } = await supabase.functions.invoke('speech-evaluation', {
        body: { audio: audioBase64, originalTranscript: transcript },
      });

      if (error) {
        console.error('Supabase function invocation error:', error);
        const specificMessage = (error.context as any)?.error || error.message;
        if (typeof specificMessage === 'string' && specificMessage.includes('Failed to fetch')) {
          throw new Error('网络错误，无法连接到AI评分服务。');
        }
        throw new Error(typeof specificMessage === 'string' ? specificMessage : '调用评分功能时发生未知错误。');
      }

      if (data.error) throw new Error(data.error);

      setEvaluationResult(data);
      toast({
        title: '评分完成',
        description: 'AI 已对您的跟读进行评分。',
      });
    } catch (error) {
      console.error('Error evaluating recording:', error);
      let description = (error as Error).message;
      if (description.includes('non-2xx status code')) {
        description = 'AI评分服务返回错误。请检查您的OpenAI API密钥是否正确配置，或稍后重试。'
      }
      toast({
        variant: 'destructive',
        title: '评分失败',
        description: description,
      });
    } finally {
      setIsEvaluating(false);
    }
  };

  const startRecording = async () => {
    handleReset(true); // Soft reset before starting a new recording
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      chunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) chunksRef.current.push(event.data);
      };

      mediaRecorder.onstop = async () => {
        const blob = new Blob(chunksRef.current, { type: 'audio/webm' });
        const url = URL.createObjectURL(blob);
        setRecordingUrl(url);
        setHasRecording(true);
        onComplete(url);
        stream.getTracks().forEach(track => track.stop());
        await evaluateRecording(blob);
      };

      mediaRecorder.start();
      setIsRecording(true);
    } catch (error) {
      console.error('Error starting recording:', error);
      alert('无法访问麦克风，请检查权限设置');
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const playOriginalAudio = () => {
    if (originalAudioRef.current) {
      if (isPlayingOriginal) originalAudioRef.current.pause();
      else originalAudioRef.current.play();
    }
  };

  const playRecording = () => {
    if (recordingAudioRef.current) {
      if (isPlayingRecording) recordingAudioRef.current.pause();
      else recordingAudioRef.current.play();
    }
  };

  const handleReset = (soft = false) => {
    if(originalAudioRef.current) originalAudioRef.current.pause();
    if(recordingAudioRef.current) recordingAudioRef.current.pause();
    
    setIsRecording(false);
    setIsPlayingOriginal(false);
    setIsPlayingRecording(false);
    setEvaluationResult(null);
    setIsEvaluating(false);
    setHasRecording(false);

    if (recordingUrl) {
      URL.revokeObjectURL(recordingUrl);
      setRecordingUrl(null);
    }
    
    if (mediaRecorderRef.current?.stream) {
      mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());
    }

    if (!soft) onReset();
  };

  return (
    <div className="bg-white rounded-2xl shadow-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-xl font-semibold text-gray-900">跟读练习</h3>
        <button
          onClick={() => handleReset(false)}
          className="text-gray-500 hover:text-gray-700 transition-colors"
        >
          <RotateCcw size={20} />
        </button>
      </div>

      <div className="space-y-6">
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-semibold text-gray-900 mb-2">练习原文：</h4>
          <p className="text-gray-800 leading-relaxed">{transcript}</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-blue-50 rounded-lg p-4">
            <h4 className="font-semibold text-blue-900 mb-3 flex items-center"><Volume2 size={20} className="mr-2" />原音播放</h4>
            <button onClick={playOriginalAudio} disabled={!audioUrl} className="w-full py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2">
              {isPlayingOriginal ? <><Pause size={20} /><span>暂停</span></> : <><Play size={20} /><span>播放原音</span></>}
            </button>
            {audioUrl && <audio ref={originalAudioRef} src={audioUrl} onPlay={() => setIsPlayingOriginal(true)} onPause={() => setIsPlayingOriginal(false)} onEnded={() => setIsPlayingOriginal(false)} />}
          </div>

          <div className="bg-red-50 rounded-lg p-4">
            <h4 className="font-semibold text-red-900 mb-3 flex items-center"><Mic size={20} className="mr-2" />录音跟读</h4>
            <button onClick={isRecording ? stopRecording : startRecording} className={`w-full py-3 rounded-lg transition-colors flex items-center justify-center space-x-2 ${isRecording ? 'bg-red-600 hover:bg-red-700 text-white animate-pulse' : 'bg-red-500 hover:bg-red-600 text-white'}`}>
              {isRecording ? <><MicOff size={20} /><span>停止录音</span></> : <><Mic size={20} /><span>开始录音</span></>}
            </button>
          </div>
        </div>

        {hasRecording && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-green-50 rounded-lg p-4">
              <h4 className="font-semibold text-green-900 mb-3 flex items-center"><Volume2 size={20} className="mr-2" />您的录音</h4>
              <button onClick={playRecording} className="w-full py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors flex items-center justify-center space-x-2">
                {isPlayingRecording ? <><Pause size={20} /><span>暂停播放</span></> : <><Play size={20} /><span>播放录音</span></>}
              </button>
              {recordingUrl && <audio ref={recordingAudioRef} src={recordingUrl} onPlay={() => setIsPlayingRecording(true)} onPause={() => setIsPlayingRecording(false)} onEnded={() => setIsPlayingRecording(false)} />}
            </div>
            
            <div className="bg-purple-50 rounded-lg p-4 flex flex-col">
              <h4 className="font-semibold text-purple-900 mb-3 flex items-center"><Star size={20} className="mr-2" />AI 智能评分</h4>
              <div className="flex-grow flex items-center justify-center">
                {isEvaluating ? (
                  <div className="flex flex-col items-center justify-center text-purple-700 py-4 space-y-2">
                    <Loader2 size={24} className="animate-spin" />
                    <span>正在智能分析您的发音...</span>
                  </div>
                ) : evaluationResult ? (
                  <div className="space-y-2 w-full">
                    <div className="text-center">
                      <p className="text-md text-purple-700">综合得分</p>
                      <p className="text-4xl font-bold text-purple-900">{evaluationResult.score}</p>
                    </div>
                    <div className="pt-2">
                      <h5 className="font-semibold text-purple-800 mb-1 text-sm">AI 评价与建议:</h5>
                      <p className="text-xs bg-purple-100 p-2 rounded-md text-purple-800">{evaluationResult.feedback}</p>
                    </div>
                    <div>
                      <h5 className="font-semibold text-purple-800 mb-1 text-sm">识别出的您的跟读内容:</h5>
                      <p className="text-xs bg-gray-100 p-2 rounded-md text-gray-700 font-mono">{evaluationResult.userTranscript || "未能识别出内容。"}</p>
                    </div>
                  </div>
                ) : (
                  <p className="text-sm text-purple-600 text-center py-4">录音完成后将自动进行AI评分。</p>
                )}
              </div>
            </div>
          </div>
        )}

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h4 className="font-semibold text-yellow-800 mb-2">练习提示：</h4>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• 先听原音，理解语音语调</li>
            <li>• 点击录音按钮开始跟读</li>
            <li>• 录音完成后可以播放对比，并查看AI评分</li>
            <li>• 多次练习直到满意为止</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default SpeakingMode;
