
import React from 'react';
import { Star, User } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

const testimonials = [
  {
    name: '<PERSON>',
    role: '大学生',
    avatar: 'https://i.pravatar.cc/150?img=1',
    text: 'AI对话功能太神奇了！它就像一个耐心的语伴，随时陪我练习口语，我的发音和流利度都提高了很多。',
    rating: 5,
  },
  {
    name: '<PERSON>',
    role: '软件工程师',
    avatar: 'https://i.pravatar.cc/150?img=3',
    text: '生词本和听力练习对我帮助最大。我可以收藏遇到的生词，然后在听力材料中巩固它们，学习效率很高。',
    rating: 5,
  },
  {
    name: '<PERSON>',
    role: '高中生',
    avatar: 'https://i.pravatar.cc/150?img=5',
    text: '这里的系统课程内容很全面，从零基础到高级都有覆盖。界面也很清爽，学习体验非常好！',
    rating: 4,
  },
];

const Testimonials = () => {
  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
      <div className="max-w-7xl mx-auto">
        <h2 className="text-4xl font-bold text-center text-gray-900 mb-16">
          听听我们用户的声音
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="flex flex-col justify-between transform hover:-translate-y-2 transition-transform duration-300 shadow-lg hover:shadow-2xl rounded-2xl">
              <CardHeader className="flex flex-row items-center gap-4 pb-4">
                <Avatar>
                  <AvatarImage src={testimonial.avatar} alt={testimonial.name} />
                  <AvatarFallback><User /></AvatarFallback>
                </Avatar>
                <div>
                  <CardTitle className="text-lg">{testimonial.name}</CardTitle>
                  <p className="text-sm text-gray-500">{testimonial.role}</p>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex mb-4">
                  {Array(5).fill(0).map((_, i) => (
                    <Star
                      key={i}
                      className={`h-5 w-5 ${i < testimonial.rating ? 'text-yellow-400' : 'text-gray-300'}`}
                      fill="currentColor"
                    />
                  ))}
                </div>
                <p className="text-gray-700">“{testimonial.text}”</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
