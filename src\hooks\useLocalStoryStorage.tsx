import { useCallback } from 'react';

// 本地存储的故事数据结构
interface LocalStory {
  id: string;
  title: string;
  description: string;
  story: string;
  storyChinese?: string;
  imageUrl?: string;
  audioUrl?: string;
  hasLocalAudio: boolean;
  lastAccessed: number;
  createdAt: number;
}

const STORAGE_KEY = 'speak_smart_stories';
const MAX_STORIES = 50; // 最多保存50个故事

export const useLocalStoryStorage = () => {
  
  // 获取所有本地故事
  const getLocalStories = useCallback((): LocalStory[] => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const stories = JSON.parse(stored) as LocalStory[];
        return stories.sort((a, b) => b.lastAccessed - a.lastAccessed);
      }
      return [];
    } catch (error) {
      console.error('Error getting local stories:', error);
      return [];
    }
  }, []);

  // 保存故事到本地
  const saveStoryToLocal = useCallback((story: any, hasLocalAudio: boolean = false): void => {
    try {
      const stories = getLocalStories();
      const now = Date.now();
      
      const localStory: LocalStory = {
        id: story.id,
        title: story.title,
        description: story.description,
        story: story.story,
        storyChinese: story.storyChinese,
        imageUrl: story.imageUrl,
        audioUrl: story.audioUrl,
        hasLocalAudio,
        lastAccessed: now,
        createdAt: story.createdAt ? new Date(story.createdAt).getTime() : now,
      };

      // 检查是否已存在
      const existingIndex = stories.findIndex(s => s.id === story.id);
      if (existingIndex >= 0) {
        // 更新现有故事
        stories[existingIndex] = { ...stories[existingIndex], ...localStory };
      } else {
        // 添加新故事
        stories.unshift(localStory);
      }

      // 限制存储数量
      if (stories.length > MAX_STORIES) {
        stories.splice(MAX_STORIES);
      }

      localStorage.setItem(STORAGE_KEY, JSON.stringify(stories));
    } catch (error) {
      console.error('Error saving story to local:', error);
    }
  }, [getLocalStories]);

  // 获取单个故事
  const getLocalStory = useCallback((storyId: string): LocalStory | null => {
    try {
      const stories = getLocalStories();
      const story = stories.find(s => s.id === storyId);
      
      if (story) {
        // 更新最后访问时间
        story.lastAccessed = Date.now();
        saveStoryToLocal(story, story.hasLocalAudio);
        return story;
      }
      
      return null;
    } catch (error) {
      console.error('Error getting local story:', error);
      return null;
    }
  }, [getLocalStories, saveStoryToLocal]);

  // 更新故事的音频状态
  const updateStoryAudioStatus = useCallback((storyId: string, hasLocalAudio: boolean): void => {
    try {
      const stories = getLocalStories();
      const storyIndex = stories.findIndex(s => s.id === storyId);
      
      if (storyIndex >= 0) {
        stories[storyIndex].hasLocalAudio = hasLocalAudio;
        stories[storyIndex].lastAccessed = Date.now();
        localStorage.setItem(STORAGE_KEY, JSON.stringify(stories));
      }
    } catch (error) {
      console.error('Error updating story audio status:', error);
    }
  }, [getLocalStories]);

  // 删除本地故事
  const deleteLocalStory = useCallback((storyId: string): void => {
    try {
      const stories = getLocalStories();
      const filteredStories = stories.filter(s => s.id !== storyId);
      localStorage.setItem(STORAGE_KEY, JSON.stringify(filteredStories));
    } catch (error) {
      console.error('Error deleting local story:', error);
    }
  }, [getLocalStories]);

  // 清理过期的故事（超过30天未访问）
  const cleanupOldStories = useCallback((): void => {
    try {
      const stories = getLocalStories();
      const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
      const activeStories = stories.filter(s => s.lastAccessed > thirtyDaysAgo);
      
      if (activeStories.length !== stories.length) {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(activeStories));
        console.log(`Cleaned up ${stories.length - activeStories.length} old stories`);
      }
    } catch (error) {
      console.error('Error cleaning up old stories:', error);
    }
  }, [getLocalStories]);

  // 获取存储统计信息
  const getStorageStats = useCallback(() => {
    try {
      const stories = getLocalStories();
      const storageSize = new Blob([localStorage.getItem(STORAGE_KEY) || '']).size;
      const storiesWithAudio = stories.filter(s => s.hasLocalAudio).length;
      
      return {
        totalStories: stories.length,
        storiesWithAudio,
        storageSize,
        lastCleanup: Date.now()
      };
    } catch (error) {
      console.error('Error getting storage stats:', error);
      return {
        totalStories: 0,
        storiesWithAudio: 0,
        storageSize: 0,
        lastCleanup: 0
      };
    }
  }, [getLocalStories]);

  // 搜索本地故事
  const searchLocalStories = useCallback((query: string): LocalStory[] => {
    try {
      const stories = getLocalStories();
      const lowercaseQuery = query.toLowerCase();
      
      return stories.filter(story => 
        story.title.toLowerCase().includes(lowercaseQuery) ||
        story.description.toLowerCase().includes(lowercaseQuery) ||
        story.story.toLowerCase().includes(lowercaseQuery)
      );
    } catch (error) {
      console.error('Error searching local stories:', error);
      return [];
    }
  }, [getLocalStories]);

  // 导出所有故事数据
  const exportStoriesData = useCallback((): string => {
    try {
      const stories = getLocalStories();
      return JSON.stringify(stories, null, 2);
    } catch (error) {
      console.error('Error exporting stories data:', error);
      return '[]';
    }
  }, [getLocalStories]);

  // 导入故事数据
  const importStoriesData = useCallback((jsonData: string): boolean => {
    try {
      const importedStories = JSON.parse(jsonData) as LocalStory[];
      const existingStories = getLocalStories();
      
      // 合并数据，避免重复
      const mergedStories = [...existingStories];
      
      importedStories.forEach(importedStory => {
        const existingIndex = mergedStories.findIndex(s => s.id === importedStory.id);
        if (existingIndex >= 0) {
          // 更新现有故事，保留最新的访问时间
          mergedStories[existingIndex] = {
            ...importedStory,
            lastAccessed: Math.max(mergedStories[existingIndex].lastAccessed, importedStory.lastAccessed)
          };
        } else {
          mergedStories.push(importedStory);
        }
      });

      // 限制数量并保存
      if (mergedStories.length > MAX_STORIES) {
        mergedStories.sort((a, b) => b.lastAccessed - a.lastAccessed);
        mergedStories.splice(MAX_STORIES);
      }

      localStorage.setItem(STORAGE_KEY, JSON.stringify(mergedStories));
      return true;
    } catch (error) {
      console.error('Error importing stories data:', error);
      return false;
    }
  }, [getLocalStories]);

  return {
    getLocalStories,
    saveStoryToLocal,
    getLocalStory,
    updateStoryAudioStatus,
    deleteLocalStory,
    cleanupOldStories,
    getStorageStats,
    searchLocalStories,
    exportStoriesData,
    importStoriesData
  };
};
