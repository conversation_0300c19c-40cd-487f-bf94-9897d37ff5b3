import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/hooks/useAuth";
import Navigation from "./components/Navigation";
import Home from "./pages/Home";
import Courses from "./pages/Courses";
import Chat from "./pages/Chat";
import Practice from "./pages/Practice";
import Pricing from "./pages/Pricing";
import Login from "./pages/Login";
import NotFound from "./pages/NotFound";
import CourseDetail from "@/pages/CourseDetail";
import Vocabulary from "./pages/Vocabulary";
import Grammar from './pages/Grammar';
import FloatingAIChat from './components/FloatingAIChat';
import Stories from './pages/Stories';
import StoryDetail from './pages/StoryDetail';
import Translate from './pages/Translate';
import Scenarios from './pages/Scenarios';
import Reading from './pages/Reading';
import Games from './pages/Games';
import Analysis from './pages/Analysis';
import Culture from './pages/Culture';

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <AuthProvider>
        <BrowserRouter>
          <div className="min-h-screen bg-white">
            <Navigation />
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/courses" element={<Courses />} />
              <Route path="/courses/:id" element={<CourseDetail />} />
              <Route path="/chat" element={<Chat />} />
              <Route path="/practice" element={<Practice />} />
              <Route path="/vocabulary" element={<Vocabulary />} />
              <Route path="/grammar" element={<Grammar />} />
              <Route path="/stories" element={<Stories />} />
              <Route path="/stories/:id" element={<StoryDetail />} />
              <Route path="/pricing" element={<Pricing />} />
              <Route path="/login" element={<Login />} />
              <Route path="/translate" element={<Translate />} />
              <Route path="/scenarios" element={<Scenarios />} />
              <Route path="/reading" element={<Reading />} />
              <Route path="/games" element={<Games />} />
              <Route path="/analysis" element={<Analysis />} />
              <Route path="/culture" element={<Culture />} />
              <Route path="*" element={<NotFound />} />
            </Routes>
            <FloatingAIChat />
          </div>
        </BrowserRouter>
      </AuthProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
