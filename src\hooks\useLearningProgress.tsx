
import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export interface LearningProgress {
  id: string;
  user_id: string;
  course_type: string;
  lesson_id: string | null;
  progress_data: any;
  score: number | null;
  completed_at: string | null;
  created_at: string;
  updated_at: string;
}

export const useLearningProgress = () => {
  const [progress, setProgress] = useState<LearningProgress[]>([]);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    if (user) {
      fetchProgress();
    } else {
      setProgress([]);
      setLoading(false);
    }
  }, [user]);

  const fetchProgress = async () => {
    try {
      const { data, error } = await supabase
        .from('learning_progress')
        .select('*')
        .eq('user_id', user?.id)
        .order('updated_at', { ascending: false });

      if (error) throw error;
      setProgress(data || []);
    } catch (error) {
      console.error('Error fetching progress:', error);
      toast({
        variant: "destructive",
        title: "加载失败",
        description: "无法加载学习进度"
      });
    } finally {
      setLoading(false);
    }
  };

  const saveProgress = async (
    courseType: string,
    lessonId: string | null,
    progressData: any,
    score?: number,
    completed?: boolean
  ) => {
    if (!user) return;

    try {
      const progressEntry = {
        user_id: user.id,
        course_type: courseType,
        lesson_id: lessonId,
        progress_data: progressData,
        score,
        completed_at: completed ? new Date().toISOString() : null,
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('learning_progress')
        .upsert(progressEntry, {
          onConflict: 'user_id,course_type,lesson_id'
        })
        .select()
        .single();

      if (error) throw error;

      // Update local state
      setProgress(prev => {
        const existing = prev.findIndex(
          p => p.course_type === courseType && p.lesson_id === lessonId
        );
        if (existing >= 0) {
          const updated = [...prev];
          updated[existing] = data;
          return updated;
        } else {
          return [data, ...prev];
        }
      });

      return data;
    } catch (error) {
      console.error('Error saving progress:', error);
      toast({
        variant: "destructive",
        title: "保存失败",
        description: "无法保存学习进度"
      });
    }
  };

  const getProgressByType = (courseType: string) => {
    return progress.filter(p => p.course_type === courseType);
  };

  const getCompletionRate = (courseType: string) => {
    const typeProgress = getProgressByType(courseType);
    if (typeProgress.length === 0) return 0;
    
    const completed = typeProgress.filter(p => p.completed_at !== null);
    return Math.round((completed.length / typeProgress.length) * 100);
  };

  return {
    progress,
    loading,
    saveProgress,
    getProgressByType,
    getCompletionRate,
    refetch: fetchProgress
  };
};
