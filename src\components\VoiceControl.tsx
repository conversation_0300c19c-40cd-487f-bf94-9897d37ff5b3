
import React, { useEffect } from 'react';
import { Mic, MicOff } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useSpeechRecognition } from '@/hooks/useSpeechRecognition';

interface VoiceControlProps {
  onTranscript: (text: string) => void;
  isProcessing: boolean;
}

const VoiceControl = ({ onTranscript, isProcessing }: VoiceControlProps) => {
  const {
    isListening,
    transcript,
    startListening,
    stopListening,
    resetTranscript,
    isSupported
  } = useSpeechRecognition();

  useEffect(() => {
    if (transcript && !isListening) {
      onTranscript(transcript);
      resetTranscript();
    }
  }, [transcript, isListening, onTranscript, resetTranscript]);

  const handleMicClick = () => {
    if (isListening) {
      stopListening();
    } else {
      startListening();
    }
  };

  if (!isSupported) {
    return (
      <div className="text-sm text-gray-500 text-center">
        您的浏览器不支持语音识别
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center space-y-2 p-4 bg-gray-50">
      <Button
        onClick={handleMicClick}
        disabled={isProcessing}
        className={`w-16 h-16 rounded-full ${
          isListening 
            ? 'bg-red-500 hover:bg-red-600 animate-pulse' 
            : 'bg-blue-500 hover:bg-blue-600'
        }`}
      >
        {isListening ? (
          <MicOff className="w-6 h-6" />
        ) : (
          <Mic className="w-6 h-6" />
        )}
      </Button>
      <span className="text-sm text-gray-600">
        {isListening ? '正在聆听...' : '点击开始语音对话'}
      </span>
    </div>
  );
};

export default VoiceControl;
