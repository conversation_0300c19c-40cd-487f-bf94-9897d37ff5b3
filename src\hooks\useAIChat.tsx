import { useState, useEffect, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  model?: string;
}

export const useAIChat = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedModel, setSelectedModel] = useState('gemini-2.0-flash-exp');
  const { toast } = useToast();
  const [isTtsLoading, setIsTtsLoading] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  const [activeSpeech, setActiveSpeech] = useState<{ timestamp: Date } | null>(null);
  
  // Cleanup audio on unmount
  useEffect(() => {
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
    };
  }, []);

  const sendMessage = async (message: string) => {
    if (!message.trim()) return;

    const userMessage: ChatMessage = {
      role: 'user',
      content: message,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setLoading(true);

    try {
      const conversationHistory = messages.map(msg => ({
        role: msg.role,
        content: msg.content
      }));

      const { data, error } = await supabase.functions.invoke('chat-ai', {
        body: { message, conversationHistory, model: selectedModel }
      });

      if (error) throw error;

      const aiMessage: ChatMessage = {
        role: 'assistant',
        content: data.response,
        timestamp: new Date(),
        model: data.model
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('Error sending message:', error);
      toast({
        variant: "destructive",
        title: "发送失败",
        description: "无法发送消息，请稍后重试"
      });
    } finally {
      setLoading(false);
    }
  };

  const clearMessages = () => {
    setMessages([]);
    if (audioRef.current) {
      audioRef.current.pause();
    }
    setActiveSpeech(null);
  };

  const playTextToSpeech = async (text: string) => {
    if (isTtsLoading) return;
    if (audioRef.current) {
      audioRef.current.pause();
    }
    
    setIsTtsLoading(true);
    
    try {
      const { data, error } = await supabase.functions.invoke('text-to-speech', {
        body: { text },
      });

      if (error || !data.audioContent) {
        throw new Error(error?.message || "无法生成音频");
      }

      const audioFormat = data.format || 'mp3'; // Default to mp3 if format is not provided
      const audioSrc = `data:audio/${audioFormat};base64,${data.audioContent}`;
      audioRef.current = new Audio(audioSrc);
      audioRef.current.play();
    } catch (err: any) {
      console.error('TTS Error:', err);
      toast({
        variant: "destructive",
        title: "语音播放失败",
        description: err.message || "请检查网络或稍后重试",
      });
    } finally {
      setIsTtsLoading(false);
    }
  };

  const toggleMessagePlayback = async (message: ChatMessage) => {
    const { timestamp, content } = message;

    if (audioRef.current) {
      audioRef.current.pause();
      if (activeSpeech?.timestamp.getTime() === timestamp.getTime()) {
        setActiveSpeech(null);
        return;
      }
    }
    
    setActiveSpeech({ timestamp });

    try {
      const { data, error } = await supabase.functions.invoke('text-to-speech', {
        body: { text: content },
      });

      if (error || !data.audioContent) {
        throw new Error(error?.message || "无法生成音频");
      }

      const audioFormat = data.format || 'mp3'; // Default to mp3 if format is not provided
      const audioSrc = `data:audio/${audioFormat};base64,${data.audioContent}`;
      audioRef.current = new Audio(audioSrc);
      audioRef.current.play();
      audioRef.current.onended = () => {
        setActiveSpeech(null);
      };
      audioRef.current.onerror = () => {
        setActiveSpeech(null);
      }
    } catch (err: any) {
      console.error('TTS Error:', err);
      toast({
        variant: "destructive",
        title: "语音播放失败",
        description: err.message || "请检查网络或稍后重试",
      });
      setActiveSpeech(null);
    }
  };

  return {
    messages,
    loading,
    isTtsLoading,
    sendMessage,
    clearMessages,
    selectedModel,
    setSelectedModel,
    playTextToSpeech,
    toggleMessagePlayback,
    activeSpeech: activeSpeech ? { timestamp: activeSpeech.timestamp, isPaused: false } : null,
  };
};
