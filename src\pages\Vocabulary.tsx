
import React from 'react';
import VocabularyList from '@/components/VocabularyList';
import { useAuth } from '@/hooks/useAuth';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';

const Vocabulary = () => {
  const { user } = useAuth();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-emerald-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-extrabold text-gray-900 sm:text-5xl">
            我的生词本
          </h1>
          <p className="mt-4 text-xl text-gray-600">
            在这里管理和复习您所有收藏的词汇。
          </p>
        </div>
        
        <div className="bg-white rounded-2xl shadow-xl p-6 sm:p-8">
          {user ? (
            <VocabularyList />
          ) : (
            <div className="text-center py-12">
              <h3 className="text-xl font-semibold text-gray-700">请先登录</h3>
              <p className="text-gray-500 mt-2 mb-6">登录后才能查看您的专属生词本哦。</p>
              <Button asChild>
                <Link to="/login">前往登录</Link>
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Vocabulary;
