
import { useState, useRef } from 'react';

export interface Exercise {
  id: string;
  title: string;
  audioUrl: string;
  transcript: string;
  questions: {
    question: string;
    options: string[];
    correct: number;
  }[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
}

export const usePracticeState = () => {
  const [currentExercise, setCurrentExercise] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [showTranscript, setShowTranscript] = useState(false);
  const [answers, setAnswers] = useState<{ [key: number]: number }>({});
  const [showResults, setShowResults] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [audioFiles, setAudioFiles] = useState<{ [key: number]: string }>({});
  const [playbackRate, setPlaybackRate] = useState(1);
  const [isLooping, setIsLooping] = useState(false);
  const [loopStart, setLoopStart] = useState<number | null>(null);
  const [loopEnd, setLoopEnd] = useState<number | null>(null);
  const [practiceMode, setPracticeMode] = useState<'listening' | 'dictation' | 'fillBlank' | 'speaking'>('listening');
  const [vocabulary, setVocabulary] = useState<string[]>([]);
  
  const audioRef = useRef<HTMLAudioElement>(null);

  return {
    // State
    currentExercise,
    isPlaying,
    currentTime,
    duration,
    showTranscript,
    answers,
    showResults,
    isUploading,
    isGenerating,
    audioFiles,
    playbackRate,
    isLooping,
    loopStart,
    loopEnd,
    practiceMode,
    vocabulary,
    audioRef,
    
    // Setters
    setCurrentExercise,
    setIsPlaying,
    setCurrentTime,
    setDuration,
    setShowTranscript,
    setAnswers,
    setShowResults,
    setIsUploading,
    setIsGenerating,
    setAudioFiles,
    setPlaybackRate,
    setIsLooping,
    setLoopStart,
    setLoopEnd,
    setPracticeMode,
    setVocabulary
  };
};
