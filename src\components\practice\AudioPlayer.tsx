
import React from 'react';
import { Play, Pause, RotateCcw, Volume2, Gauge, Repeat } from 'lucide-react';

interface AudioPlayerProps {
  title: string;
  transcript: string;
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  showTranscript: boolean;
  hasAudio: boolean;
  playbackRate: number;
  isLooping: boolean;
  loopStart: number | null;
  loopEnd: number | null;
  onPlayPause: () => void;
  onReset: () => void;
  onToggleTranscript: () => void;
  onPlaybackRateChange: (rate: number) => void;
  onToggleLoop: () => void;
  onSetLoopStart: () => void;
  onSetLoopEnd: () => void;
  onClearLoop: () => void;
}

const AudioPlayer = ({
  title,
  transcript,
  isPlaying,
  currentTime,
  duration,
  showTranscript,
  hasAudio,
  playbackRate,
  isLooping,
  loopStart,
  loopEnd,
  onPlayPause,
  onReset,
  onToggleTranscript,
  onPlaybackRateChange,
  onToggleLoop,
  onSetLoopStart,
  onSetLoopEnd,
  onClearLoop
}: AudioPlayerProps) => {
  const playbackRates = [0.5, 0.75, 1, 1.25, 1.5];

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="bg-white rounded-2xl shadow-lg mb-6 p-6">
      <h3 className="text-xl font-semibold text-gray-900 mb-4">{title}</h3>
      
      <div className="bg-gradient-to-r from-blue-500 to-emerald-500 rounded-xl p-6 text-white mb-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4">
            <button
              onClick={onPlayPause}
              disabled={!hasAudio}
              className="bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full p-3 transition-colors disabled:opacity-50"
            >
              {isPlaying ? (
                <Pause size={24} />
              ) : (
                <Play size={24} />
              )}
            </button>
            <button
              onClick={onReset}
              disabled={!hasAudio}
              className="bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full p-2 transition-colors disabled:opacity-50"
            >
              <RotateCcw size={20} />
            </button>
          </div>
          <div className="flex items-center space-x-2">
            <Volume2 size={20} />
            <span className="text-sm">音量</span>
          </div>
        </div>
        
        {/* Progress Bar */}
        <div className="bg-white bg-opacity-20 rounded-full h-2 mb-2 relative">
          <div 
            className="bg-white rounded-full h-2 transition-all duration-300"
            style={{ width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }}
          ></div>
          {/* Loop markers */}
          {loopStart !== null && (
            <div 
              className="absolute top-0 h-2 w-1 bg-yellow-400 rounded"
              style={{ left: `${(loopStart / duration) * 100}%` }}
            ></div>
          )}
          {loopEnd !== null && (
            <div 
              className="absolute top-0 h-2 w-1 bg-yellow-400 rounded"
              style={{ left: `${(loopEnd / duration) * 100}%` }}
            ></div>
          )}
        </div>
        <div className="text-sm opacity-90">
          {formatTime(currentTime)} / {formatTime(duration || 0)}
        </div>
        
        {!hasAudio && (
          <div className="text-sm opacity-90 mt-2">
            请先上传音频文件
          </div>
        )}
      </div>

      {/* Controls */}
      <div className="flex flex-wrap gap-4 mb-4">
        {/* Playback Speed */}
        <div className="flex items-center space-x-2">
          <Gauge size={16} className="text-gray-600" />
          <span className="text-sm text-gray-600">播放速度:</span>
          <select
            value={playbackRate}
            onChange={(e) => onPlaybackRateChange(Number(e.target.value))}
            disabled={!hasAudio}
            className="px-2 py-1 border border-gray-300 rounded text-sm disabled:opacity-50"
          >
            {playbackRates.map(rate => (
              <option key={rate} value={rate}>{rate}x</option>
            ))}
          </select>
        </div>

        {/* AB Loop Controls */}
        <div className="flex items-center space-x-2">
          <Repeat size={16} className="text-gray-600" />
          <button
            onClick={onSetLoopStart}
            disabled={!hasAudio}
            className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-sm hover:bg-yellow-200 disabled:opacity-50"
          >
            设置A点
          </button>
          <button
            onClick={onSetLoopEnd}
            disabled={!hasAudio || loopStart === null}
            className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-sm hover:bg-yellow-200 disabled:opacity-50"
          >
            设置B点
          </button>
          <button
            onClick={onToggleLoop}
            disabled={!hasAudio || loopStart === null || loopEnd === null}
            className={`px-2 py-1 rounded text-sm disabled:opacity-50 ${
              isLooping 
                ? 'bg-green-100 text-green-800 hover:bg-green-200' 
                : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
            }`}
          >
            {isLooping ? 'AB循环开' : 'AB循环关'}
          </button>
          <button
            onClick={onClearLoop}
            disabled={!hasAudio}
            className="px-2 py-1 bg-red-100 text-red-800 rounded text-sm hover:bg-red-200 disabled:opacity-50"
          >
            清除
          </button>
        </div>
      </div>

      {/* Loop Info */}
      {(loopStart !== null || loopEnd !== null) && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
          <div className="text-sm text-yellow-800">
            循环区间: {loopStart !== null ? formatTime(loopStart) : '--'} - {loopEnd !== null ? formatTime(loopEnd) : '--'}
            {isLooping && <span className="ml-2 font-semibold">（循环播放中）</span>}
          </div>
        </div>
      )}

      <div className="flex justify-center">
        <button
          onClick={onToggleTranscript}
          className="px-6 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
        >
          {showTranscript ? '隐藏' : '显示'}原文
        </button>
      </div>

      {showTranscript && (
        <div className="mt-4 p-4 bg-gray-50 rounded-xl">
          <h4 className="font-semibold text-gray-900 mb-2">原文：</h4>
          <pre className="text-gray-700 whitespace-pre-wrap font-sans">{transcript}</pre>
        </div>
      )}
    </div>
  );
};

export default AudioPlayer;
