
import React from 'react';

interface ClickableTextProps {
  text: string;
  onWordClick: (word: string, context: string) => void;
}

const ClickableText = ({ text, onWordClick }: ClickableTextProps) => {
  // Split text by newlines. Each line becomes a separate context.
  const lines = text.split('\n');

  return (
    <div className="leading-relaxed">
      {lines.map((line, lineIndex) => {
        // Render a paragraph for each line to maintain structure.
        // Empty lines will act as paragraph breaks.
        return (
          <p key={lineIndex} className="whitespace-pre-wrap">
            {line.split(/(\s+|[.,!?;:"*]+)/g).filter(Boolean).map((segment, segmentIndex) => {
              const cleanedWord = segment.replace(/[.,!?;:"*]/g, '').trim();
              const isClickable = /^[a-zA-Z]+(-[a-zA-Z]+)*$/.test(cleanedWord) && cleanedWord.length > 1;

              if (isClickable) {
                return (
                  <span
                    key={segmentIndex}
                    onClick={() => onWordClick(cleanedWord, line)}
                    className="cursor-pointer hover:bg-yellow-200 transition-colors rounded px-1"
                  >
                    {segment}
                  </span>
                );
              }
              return <span key={segmentIndex}>{segment}</span>;
            })}
          </p>
        );
      })}
    </div>
  );
};

export default ClickableText;
