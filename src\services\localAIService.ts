// 本地 AI 服务 - 支持多种 AI 提供商
export interface AIConfig {
  provider: 'minimax' | 'openai' | 'ollama' | 'siliconflow' | 'alicloud';
  apiKey?: string;
  baseUrl?: string;
  model?: string;
  groupId?: string; // MiniMax 专用
  region?: string; // 阿里云专用
}

export interface StoryGenerationResult {
  title: string;
  description: string;
  story: string;
  storyChinese?: string;
  imageUrl?: string;
}

export interface ChatResponse {
  response: string;
  model: string;
}

class LocalAIService {
  private config: AIConfig | null = null;

  // 设置 AI 配置
  setConfig(config: AIConfig) {
    this.config = config;
  }

  // 检查配置是否有效
  isConfigured(): boolean {
    return !!(this.config?.apiKey || this.config?.provider === 'ollama');
  }

  // 生成故事
  async generateStory(prompt: string): Promise<StoryGenerationResult> {
    if (!this.config) {
      throw new Error('AI 配置未设置，请先调用 setConfig()');
    }

    const storyPrompt = `请根据以下提示创作一个英文故事，并提供中文翻译：

提示：${prompt}

请按照以下JSON格式返回：
{
  "title": "故事标题（英文）",
  "description": "故事简介（中文，50字以内）",
  "story": "完整的英文故事（200-500词）",
  "storyChinese": "中文翻译"
}

要求：
1. 故事要有趣且适合英语学习
2. 使用简单易懂的英语词汇
3. 包含对话和描述
4. 中文翻译要准确自然`;

    try {
      const response = await this.callAI(storyPrompt);
      return this.parseStoryResponse(response.response);
    } catch (error) {
      console.error('故事生成失败:', error);
      throw error;
    }
  }

  // AI 聊天
  async chat(message: string, conversationHistory: any[] = []): Promise<ChatResponse> {
    if (!this.config) {
      throw new Error('AI 配置未设置，请先调用 setConfig()');
    }

    const systemPrompt = `你是一个专业的英语学习助手。请用中文回答问题，帮助用户学习英语。你可以解释语法、词汇、发音，提供例句，纠正错误，并给出学习建议。请保持回答简洁明了。

用户消息：${message}`;

    try {
      return await this.callAI(systemPrompt, conversationHistory);
    } catch (error) {
      console.error('AI 聊天失败:', error);
      throw error;
    }
  }

  // 获取单词定义
  async getWordDefinition(word: string, context: string): Promise<any> {
    const prompt = `请分析单词"${word}"在以下语境中的含义：

语境：${context}

请按照以下JSON格式返回：
{
  "word": "${word}",
  "baseForm": "单词原形",
  "partOfSpeech": "词性",
  "phonetic": "音标",
  "definition": "英文释义",
  "chineseDefinition": "中文释义",
  "example": "英文例句",
  "chineseExample": "中文例句翻译"
}`;

    try {
      const response = await this.callAI(prompt);
      return JSON.parse(response.response);
    } catch (error) {
      console.error('单词定义获取失败:', error);
      throw error;
    }
  }

  // 调用 AI API
  private async callAI(prompt: string, conversationHistory: any[] = []): Promise<ChatResponse> {
    switch (this.config!.provider) {
      case 'minimax':
        return await this.callMiniMaxAPI(prompt, conversationHistory);
      case 'siliconflow':
        return await this.callSiliconFlowAPI(prompt, conversationHistory);
      case 'openai':
        return await this.callOpenAIAPI(prompt, conversationHistory);
      case 'ollama':
        return await this.callOllamaAPI(prompt, conversationHistory);
      case 'alicloud':
        return await this.callAliCloudAPI(prompt, conversationHistory);
      default:
        throw new Error(`不支持的 AI 提供商: ${this.config!.provider}`);
    }
  }

  // MiniMax API 调用
  private async callMiniMaxAPI(prompt: string, conversationHistory: any[]): Promise<ChatResponse> {
    const response = await fetch(`https://api.minimaxi.com/v1/text/chatcompletion_v2?GroupId=${this.config!.groupId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config!.apiKey}`,
      },
      body: JSON.stringify({
        model: this.config!.model || 'abab6.5s-chat',
        messages: [
          ...conversationHistory,
          { role: 'user', content: prompt }
        ],
        stream: false,
        temperature: 0.7,
        max_tokens: 2000,
      }),
    });

    if (!response.ok) {
      throw new Error(`MiniMax API 错误: ${response.status}`);
    }

    const data = await response.json();
    
    if (data.base_resp?.status_code !== 0) {
      throw new Error(`MiniMax API 错误: ${data.base_resp?.status_msg}`);
    }

    return {
      response: data.choices[0]?.message?.content || '',
      model: this.config!.model || 'abab6.5s-chat'
    };
  }

  // SiliconFlow API 调用
  private async callSiliconFlowAPI(prompt: string, conversationHistory: any[]): Promise<ChatResponse> {
    const response = await fetch('https://api.siliconflow.cn/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config!.apiKey}`,
      },
      body: JSON.stringify({
        model: this.config!.model || 'Qwen/Qwen2.5-7B-Instruct',
        messages: [
          ...conversationHistory,
          { role: 'user', content: prompt }
        ],
        stream: false,
        temperature: 0.7,
        max_tokens: 2000,
      }),
    });

    if (!response.ok) {
      throw new Error(`SiliconFlow API 错误: ${response.status}`);
    }

    const data = await response.json();
    return {
      response: data.choices[0]?.message?.content || '',
      model: this.config!.model || 'Qwen/Qwen2.5-7B-Instruct'
    };
  }

  // OpenAI API 调用
  private async callOpenAIAPI(prompt: string, conversationHistory: any[]): Promise<ChatResponse> {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config!.apiKey}`,
      },
      body: JSON.stringify({
        model: this.config!.model || 'gpt-3.5-turbo',
        messages: [
          ...conversationHistory,
          { role: 'user', content: prompt }
        ],
        stream: false,
        temperature: 0.7,
        max_tokens: 2000,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API 错误: ${response.status}`);
    }

    const data = await response.json();
    return {
      response: data.choices[0]?.message?.content || '',
      model: this.config!.model || 'gpt-3.5-turbo'
    };
  }

  // Ollama API 调用（本地）
  private async callOllamaAPI(prompt: string, conversationHistory: any[]): Promise<ChatResponse> {
    const baseUrl = this.config!.baseUrl || 'http://localhost:11434';
    const model = this.config!.model || 'llama2';

    const response = await fetch(`${baseUrl}/api/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: model,
        messages: [
          ...conversationHistory,
          { role: 'user', content: prompt }
        ],
        stream: false,
      }),
    });

    if (!response.ok) {
      throw new Error(`Ollama API 错误: ${response.status}`);
    }

    const data = await response.json();
    return {
      response: data.message?.content || '',
      model: model
    };
  }

  // 阿里云 API 调用 - 使用 OpenAI 兼容格式
  private async callAliCloudAPI(prompt: string, conversationHistory: any[]): Promise<ChatResponse> {
    const model = this.config!.model || 'qwen-turbo';
    const baseUrl = 'https://dashscope.aliyuncs.com/compatible-mode/v1';

    const response = await fetch(`${baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config!.apiKey}`,
      },
      body: JSON.stringify({
        model: model,
        messages: [
          ...conversationHistory,
          { role: 'user', content: prompt }
        ],
        temperature: 0.7,
        max_tokens: 2000,
        top_p: 0.8
      }),
    });

    if (!response.ok) {
      throw new Error(`阿里云 API 错误: ${response.status}`);
    }

    const data = await response.json();

    if (data.code && data.code !== '200') {
      throw new Error(`阿里云 API 错误: ${data.message || data.code}`);
    }

    return {
      response: data.output?.choices?.[0]?.message?.content || '',
      model: model
    };
  }

  // 解析故事响应
  private parseStoryResponse(response: string): StoryGenerationResult {
    try {
      // 尝试解析 JSON
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          title: parsed.title || '未命名故事',
          description: parsed.description || '一个有趣的故事',
          story: parsed.story || response,
          storyChinese: parsed.storyChinese,
          imageUrl: parsed.imageUrl
        };
      }
    } catch (error) {
      console.warn('JSON 解析失败，使用文本解析:', error);
    }

    // 如果 JSON 解析失败，使用文本解析
    return {
      title: this.extractTitle(response) || '生成的故事',
      description: '一个由 AI 生成的有趣故事',
      story: response,
      storyChinese: undefined
    };
  }

  // 从文本中提取标题
  private extractTitle(text: string): string | null {
    const titlePatterns = [
      /Title:\s*(.+)/i,
      /标题[：:]\s*(.+)/,
      /^(.+)$/m // 第一行作为标题
    ];

    for (const pattern of titlePatterns) {
      const match = text.match(pattern);
      if (match && match[1]) {
        return match[1].trim().substring(0, 50);
      }
    }

    return null;
  }

  // 测试 AI 连接
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.chat('Hello, this is a test message.');
      return response.response.length > 0;
    } catch (error) {
      console.error('AI 连接测试失败:', error);
      return false;
    }
  }
}

// 创建单例实例
export const localAIService = new LocalAIService();

// 导出类型和服务
export default LocalAIService;
