
import React from 'react';
import { CheckCircle, XCircle } from 'lucide-react';

interface Question {
  question: string;
  options: string[];
  correct: number;
}

interface QuestionsListProps {
  questions: Question[];
  answers: { [key: number]: number };
  showResults: boolean;
  onAnswerSelect: (questionIndex: number, answerIndex: number) => void;
}

const QuestionsList = ({ questions, answers, showResults, onAnswerSelect }: QuestionsListProps) => {
  return (
    <div className="space-y-6">
      {questions.map((question, questionIndex) => (
        <div key={questionIndex} className="border-b border-gray-200 pb-6 last:border-b-0">
          <h4 className="font-medium text-gray-900 mb-4">
            {questionIndex + 1}. {question.question}
          </h4>
          <div className="space-y-2">
            {question.options.map((option, optionIndex) => (
              <label
                key={optionIndex}
                className={`flex items-center p-3 rounded-lg cursor-pointer transition-colors ${
                  answers[questionIndex] === optionIndex
                    ? 'bg-blue-50 border-2 border-blue-500'
                    : 'bg-gray-50 hover:bg-gray-100 border-2 border-transparent'
                } ${
                  showResults
                    ? optionIndex === question.correct
                      ? 'bg-green-50 border-green-500'
                      : answers[questionIndex] === optionIndex && optionIndex !== question.correct
                      ? 'bg-red-50 border-red-500'
                      : ''
                    : ''
                }`}
              >
                <input
                  type="radio"
                  name={`question-${questionIndex}`}
                  value={optionIndex}
                  checked={answers[questionIndex] === optionIndex}
                  onChange={() => onAnswerSelect(questionIndex, optionIndex)}
                  className="mr-3"
                  disabled={showResults}
                />
                <span className="flex-1">{option}</span>
                {showResults && optionIndex === question.correct && (
                  <CheckCircle className="text-green-500" size={20} />
                )}
                {showResults && answers[questionIndex] === optionIndex && optionIndex !== question.correct && (
                  <XCircle className="text-red-500" size={20} />
                )}
              </label>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

export default QuestionsList;
