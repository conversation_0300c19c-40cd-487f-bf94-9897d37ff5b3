
-- 创建故事表来存储用户生成的故事
CREATE TABLE public.stories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  story TEXT NOT NULL,
  story_chinese TEXT,
  image_url TEXT,
  audio_url TEXT,
  prompt TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- 启用行级安全
ALTER TABLE public.stories ENABLE ROW LEVEL SECURITY;

-- 创建策略让用户查看所有公开故事和自己的私人故事
CREATE POLICY "Users can view all stories" 
  ON public.stories 
  FOR SELECT 
  USING (true);

-- 创建策略让用户插入自己的故事
CREATE POLICY "Users can create their own stories" 
  ON public.stories 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- 创建策略让用户更新自己的故事
CREATE POLICY "Users can update their own stories" 
  ON public.stories 
  FOR UPDATE 
  USING (auth.uid() = user_id);

-- 创建策略让用户删除自己的故事
CREATE POLICY "Users can delete their own stories" 
  ON public.stories 
  FOR DELETE 
  USING (auth.uid() = user_id);

-- 创建存储桶用于存储音频文件
INSERT INTO storage.buckets (id, name, public) VALUES ('story-audio', 'story-audio', true);

-- 为音频存储桶创建策略
CREATE POLICY "Anyone can view audio files" ON storage.objects
  FOR SELECT USING (bucket_id = 'story-audio');

CREATE POLICY "Authenticated users can upload audio files" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'story-audio' AND 
    auth.role() = 'authenticated'
  );

CREATE POLICY "Users can update their own audio files" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'story-audio' AND 
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their own audio files" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'story-audio' AND 
    auth.uid()::text = (storage.foldername(name))[1]
  );
