
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const googleAIApiKey = Deno.env.get('GOOGLE_AI_API_KEY');

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { prompt } = await req.json();
    if (!prompt) {
      throw new Error('请输入故事提示。');
    }

    if (!googleAIApiKey) {
      throw new Error('GOOGLE_AI_API_KEY 未在 Supabase 中设置。');
    }

    const API_ENDPOINT = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=${googleA<PERSON><PERSON><PERSON><PERSON>}`;

    const combinedPrompt = `You are a creative storyteller for children. Based on the user's prompt, create an engaging story in English. The story should be a few paragraphs long. Respond with a JSON object containing "title" (the story title), "description" (a 2-3 sentence summary), and "story" (the full story text). Only return the JSON object, do not add any other text or markdown formatting.\n\nUser Prompt: ${prompt}`;

    const requestBody = {
      contents: [{
        parts: [{ text: combinedPrompt }]
      }],
      generationConfig: {
        response_mime_type: "application/json",
      },
      safetySettings: [
        { "category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE" },
        { "category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_MEDIUM_AND_ABOVE" },
        { "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_MEDIUM_AND_ABOVE" },
        { "category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE" }
      ]
    };

    const storyResponse = await fetch(API_ENDPOINT, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestBody),
    });

    if (!storyResponse.ok) {
        const errorBody = await storyResponse.text();
        console.error('Google AI API 错误:', errorBody);
        let errorMessage = `Google AI API 请求失败，状态码 ${storyResponse.status}.`;
        try {
            const errorJson = JSON.parse(errorBody);
            if (errorJson.error && errorJson.error.message) {
                errorMessage += ` 详情: ${errorJson.error.message}`;
            }
        } catch(e) {
            errorMessage += ` 详情: ${errorBody}`;
        }
        throw new Error(errorMessage);
    }

    const responseData = await storyResponse.json();

    if (!responseData.candidates || responseData.candidates.length === 0) {
        let errorMessage = 'AI 未能生成响应。';
        if (responseData.promptFeedback && responseData.promptFeedback.blockReason) {
            errorMessage += ` 原因: 内容可能不符合安全政策(${responseData.promptFeedback.blockReason})。`;
        } else {
            errorMessage += ' 来自 AI 的响应为空，请重试。';
        }
        console.error('Google AI API 响应缺少内容:', JSON.stringify(responseData, null, 2));
        throw new Error(errorMessage);
    }

    if (!responseData.candidates[0].content?.parts?.[0]?.text) {
        console.error('Google AI API 响应缺少内容部分:', JSON.stringify(responseData, null, 2));
        throw new Error('AI 未能生成响应。响应中缺少故事内容。');
    }
    
    const storyContentRaw = responseData.candidates[0].content.parts[0].text;
    
    let storyContent = storyContentRaw.trim();
    if (storyContent.startsWith('```json')) {
      storyContent = storyContent.slice('```json'.length, -3).trim();
    } else if (storyContent.startsWith('```')) {
      storyContent = storyContent.slice(3, -3).trim();
    }
    
    let storyData;
    try {
        storyData = JSON.parse(storyContent);
    } catch (e) {
        console.error('从 AI 响应解析 JSON 失败:', storyContentRaw);
        console.error('解析错误:', e);
        throw new Error('AI 返回了无效的数据格式，无法解析。');
    }
    
    const { title, description, story } = storyData;

    if (!title || !description || !story) {
      console.error('从 AI 获取的数据不完整:', storyData);
      throw new Error('未能从 AI 响应中生成完整的故事内容。');
    }

    return new Response(
      JSON.stringify({ title, description, story }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('generate-story 函数出错:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});
