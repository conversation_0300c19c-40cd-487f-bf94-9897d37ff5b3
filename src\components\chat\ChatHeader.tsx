
import React from 'react';
import { Bo<PERSON>, Trash2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import AIModelSelector from '@/components/AIModelSelector';

interface ChatHeaderProps {
  selectedModel: string;
  onModelChange: (model: string) => void;
  onClearMessages: () => void;
}

const ChatHeader = ({ selectedModel, onModelChange, onClearMessages }: ChatHeaderProps) => {
  return (
    <div className="flex items-center justify-between p-4 border-b bg-gradient-to-r from-blue-600 to-emerald-600 text-white rounded-t-lg">
      <div className="flex items-center space-x-2">
        <Bot className="w-6 h-6" />
        <h3 className="text-lg font-semibold">AI 英语助手</h3>
      </div>
      <div className="flex items-center space-x-2">
        <AIModelSelector 
          selectedModel={selectedModel} 
          onModelChange={onModelChange}
        />
        <Button
          variant="ghost"
          size="sm"
          onClick={onClearMessages}
          className="text-white hover:bg-white/20"
        >
          <Trash2 className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
};

export default ChatHeader;
