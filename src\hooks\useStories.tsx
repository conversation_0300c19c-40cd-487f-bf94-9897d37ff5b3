
import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/components/ui/use-toast';

interface Story {
  id: string;
  title: string;
  description: string;
  story: string;
  storyChinese?: string;
  imageUrl?: string;
  audioUrl?: string;
  prompt?: string;
  userId?: string;
  createdAt?: string;
  updatedAt?: string;
}

export const useStories = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // 从数据库获取所有故事
  const { data: stories = [], isLoading } = useQuery({
    queryKey: ['stories'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('stories')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      return data.map((story: any) => ({
        id: story.id,
        title: story.title,
        description: story.description,
        story: story.story,
        storyChinese: story.story_chinese,
        imageUrl: story.image_url,
        audioUrl: story.audio_url,
        prompt: story.prompt,
        userId: story.user_id,
        createdAt: story.created_at,
        updatedAt: story.updated_at,
      }));
    },
  });

  // 生成新故事的mutation
  const generateStoryMutation = useMutation({
    mutationFn: async (prompt: string): Promise<Story> => {
      // 调用AI生成故事
      const { data: aiData, error: aiError } = await supabase.functions.invoke('generate-story', {
        body: { prompt },
      });
      
      if (aiError) throw new Error(aiError.message);
      if (!aiData.title || !aiData.description || !aiData.story) {
        throw new Error('AI返回的数据不完整，请重试。');
      }

      // 保存到数据库
      const { data: user } = await supabase.auth.getUser();
      const { data: dbData, error: dbError } = await supabase
        .from('stories')
        .insert([
          {
            title: aiData.title,
            description: aiData.description,
            story: aiData.story,
            story_chinese: aiData.storyChinese || '',
            image_url: aiData.imageUrl || null,
            prompt: prompt,
            user_id: user.user?.id || null,
          }
        ])
        .select()
        .single();

      if (dbError) throw new Error(dbError.message);

      return {
        id: dbData.id,
        title: dbData.title,
        description: dbData.description,
        story: dbData.story,
        storyChinese: dbData.story_chinese,
        imageUrl: dbData.image_url,
        audioUrl: dbData.audio_url,
        prompt: dbData.prompt,
        userId: dbData.user_id,
        createdAt: dbData.created_at,
        updatedAt: dbData.updated_at,
      };
    },
    onSuccess: (newStory) => {
      // 更新缓存
      queryClient.setQueryData(['stories'], (oldStories: Story[] = []) => [
        newStory,
        ...oldStories
      ]);
      
      toast({
        title: "故事创作成功！",
        description: `快来看看关于"${newStory.title}"的新故事吧。`,
      });
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "故事生成失败",
        description: error.message,
      });
    }
  });

  // 更新故事音频URL的mutation
  const updateStoryAudioMutation = useMutation({
    mutationFn: async ({ storyId, audioUrl }: { storyId: string; audioUrl: string }) => {
      const { error } = await supabase
        .from('stories')
        .update({ audio_url: audioUrl })
        .eq('id', storyId);

      if (error) throw error;
      return { storyId, audioUrl };
    },
    onSuccess: ({ storyId, audioUrl }) => {
      // 更新缓存中的故事
      queryClient.setQueryData(['stories'], (oldStories: Story[] = []) =>
        oldStories.map(story =>
          story.id === storyId ? { ...story, audioUrl } : story
        )
      );
    },
  });

  return {
    stories,
    isLoading,
    generateStory: generateStoryMutation.mutate,
    isGenerating: generateStoryMutation.isPending,
    updateStoryAudio: updateStoryAudioMutation.mutate,
    isUpdatingAudio: updateStoryAudioMutation.isPending,
  };
};
