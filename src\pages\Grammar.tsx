
import React, { useState, useRef, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>les, Copy, MessageSquareQuote, Volume2 } from 'lucide-react';
import { type Change, diffChars } from 'diff';

const Grammar = () => {
  const [text, setText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isTtsLoading, setIsTtsLoading] = useState(false);
  const [correctionResult, setCorrectionResult] = useState<{ correctedText: string; diff: Change[]; explanation: string; } | null>(null);
  const { toast } = useToast();
  const audioRef = useRef<HTMLAudioElement | null>(null);

  useEffect(() => {
    // Cleanup audio on component unmount
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
    };
  }, []);

  const handleCheck = async () => {
    if (!text.trim()) {
      toast({
        variant: "destructive",
        title: "请输入内容",
        description: "要检查的文本不能为空。",
      });
      return;
    }
    
    setIsLoading(true);
    setCorrectionResult(null);

    try {
      const prompt = `You are an English grammar correction expert. Your task is to correct the provided English text and explain the corrections in Chinese.

You MUST respond with a single, valid JSON object. Do not include any text outside of the JSON object.
The JSON object must have two keys:
1. "correctedText": A string containing the fully corrected English text.
2. "explanation": A string containing a concise explanation of the grammatical changes made, in a friendly tone. The explanation must be in Chinese.

Original text:
---
${text}
---

Your JSON response:`;

      const { data, error } = await supabase.functions.invoke('chat-ai', {
        body: { 
          message: prompt, 
          conversationHistory: [],
        }
      });

      if (error) throw error;

      const rawResponse = data.response;
      // The AI might wrap the JSON in markdown fences. Let's remove them.
      const jsonString = rawResponse.replace(/^```json\n?|```$/g, '');
      const responseData = JSON.parse(jsonString);

      const { correctedText, explanation } = responseData;

      if (!correctedText || typeof correctedText !== 'string') {
        throw new Error("AI response did not contain valid 'correctedText'.");
      }
      
      const differences = diffChars(text, correctedText);
      
      setCorrectionResult({ 
        correctedText, 
        diff: differences, 
        explanation: explanation || "AI 未提供修改说明。" 
      });

      toast({
        title: "检查完成",
        description: "已生成语法修改建议和说明。",
      });
    } catch (error) {
      console.error('Error checking grammar:', error);
      const isJsonError = error instanceof Error && (error.name === 'SyntaxError' || error.message.includes('JSON'));
      toast({
        variant: "destructive",
        title: "检查失败",
        description: isJsonError ? "AI返回的数据格式有误，请稍后重试。" : "无法连接到AI服务，请稍后重试。",
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleCopy = () => {
    if (correctionResult?.correctedText) {
      navigator.clipboard.writeText(correctionResult.correctedText);
      toast({
        title: "已复制",
        description: "修改后的文本已复制到剪贴板。",
      });
    }
  };

  const handlePlayCorrectedText = async () => {
    if (!correctionResult?.correctedText || isTtsLoading) return;

    if (audioRef.current) {
      audioRef.current.pause();
    }

    setIsTtsLoading(true);

    try {
      const { data, error } = await supabase.functions.invoke('text-to-speech', {
        body: { text: correctionResult.correctedText },
      });

      if (error || !data.audioContent) {
        throw new Error(error?.message || "无法生成音频");
      }
      
      const audioFormat = data.format || 'mp3';
      const audioSrc = `data:audio/${audioFormat};base64,${data.audioContent}`;
      audioRef.current = new Audio(audioSrc);
      
      audioRef.current.onended = () => {
        setIsTtsLoading(false);
      };
      audioRef.current.onerror = () => {
        toast({ variant: "destructive", title: "语音播放失败" });
        setIsTtsLoading(false);
      };

      audioRef.current.play();
    } catch (err: any) {
      console.error('TTS Error:', err);
      toast({
        variant: "destructive",
        title: "语音生成失败",
        description: err.message || "请检查网络或稍后重试",
      });
      setIsTtsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-cyan-50 to-blue-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-extrabold text-gray-900 sm:text-5xl">
            智能语法纠错
          </h1>
          <p className="mt-4 text-xl text-gray-600">
            输入一段英文，AI 将会帮你分析语法错误并提供修改建议。
          </p>
        </div>
        
        <div className="bg-white rounded-2xl shadow-xl p-6 sm:p-8">
          <div className="flex flex-col gap-6">
            <Textarea
              placeholder="在这里输入或粘贴你的英文段落..."
              value={text}
              onChange={(e) => setText(e.target.value)}
              className="min-h-[200px] text-base"
            />
            <Button onClick={handleCheck} disabled={isLoading} size="lg">
              {isLoading ? '正在分析...' : (
                <>
                  <Sparkles className="mr-2 h-5 w-5" />
                  开始检查
                </>
              )}
            </Button>
          </div>
          
          {isLoading && (
            <div className="mt-8 space-y-4">
              <div className="bg-gray-200 h-8 w-1/4 rounded animate-pulse"></div>
              <div className="bg-gray-200 h-24 w-full rounded animate-pulse"></div>
            </div>
          )}

          {correctionResult && (
            <Card className="mt-8">
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>修改建议</CardTitle>
                <div className="flex items-center space-x-1">
                  <Button variant="ghost" size="icon" onClick={handlePlayCorrectedText} disabled={isTtsLoading}>
                    <Volume2 className={`h-5 w-5 ${isTtsLoading ? 'text-blue-500 animate-pulse' : ''}`} />
                  </Button>
                  <Button variant="ghost" size="icon" onClick={handleCopy}>
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="text-base text-gray-800 whitespace-pre-wrap p-4 bg-gray-50 rounded-md border">
                  {correctionResult.diff.map((part, index) => {
                    const style = {
                      backgroundColor: part.added ? 'rgba(46, 160, 67, 0.2)' : part.removed ? 'rgba(244, 67, 54, 0.2)' : 'transparent',
                      textDecoration: part.removed ? 'line-through' : 'none',
                    };
                    return (
                      <span key={index} style={style}>
                        {part.value}
                      </span>
                    );
                  })}
                </div>

                {correctionResult.explanation && (
                  <div className="space-y-3">
                    <h4 className="text-lg font-semibold flex items-center text-gray-800">
                      <MessageSquareQuote className="mr-2 h-5 w-5 text-blue-500" />
                      修改说明
                    </h4>
                    <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                      <p className="text-gray-700 whitespace-pre-wrap">{correctionResult.explanation}</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default Grammar;
