# DashScope 连接故障排除指南

## 🔍 常见连接失败原因

### 1. **API Key 问题**
- ❌ **API Key 错误或过期**
- ❌ **API Key 格式不正确**
- ❌ **没有开通相应服务**

### 2. **模型权限问题**
- ❌ **新模型需要特殊权限** (如 qwen2.5-omni-7b)
- ❌ **账号等级不够**
- ❌ **地区限制**

### 3. **网络问题**
- ❌ **网络连接不稳定**
- ❌ **防火墙阻止**
- ❌ **代理设置问题**

### 4. **配置问题**
- ❌ **API 端点错误**
- ❌ **请求格式不正确**
- ❌ **参数设置错误**

## 🛠️ 解决方案

### 步骤1: 验证 API Key

1. **登录阿里云控制台**
   - 访问 [DashScope 控制台](https://dashscope.console.aliyun.com/)
   - 确认已开通服务

2. **检查 API Key**
   - 确保 API Key 格式正确 (通常以 `sk-` 开头)
   - 确认 API Key 没有过期
   - 重新生成 API Key 试试

3. **确认服务开通状态**
   - 通义千问 ✅
   - 通义万相 ✅  
   - Qwen-TTS ✅

### 步骤2: 使用稳定模型

我已经更新了默认配置，使用更稳定的模型：

```typescript
// 推荐的稳定配置
const stableConfig = {
  textModel: 'qwen-turbo',      // 稳定可靠
  imageModel: 'wanx-v1',        // 基础稳定
  audioModel: 'qwen-tts-v1'     // 语音合成
};
```

### 步骤3: 测试连接

使用我提供的测试页面 `test-local-ai.html`:

1. **打开测试页面**
2. **配置阿里云信息**
3. **点击"测试连接"**
4. **查看详细错误信息**

### 步骤4: 检查错误信息

常见错误码和解决方案：

| 错误码 | 错误信息 | 解决方案 |
|--------|----------|----------|
| 400 | Invalid API Key | 检查 API Key 格式 |
| 401 | Unauthorized | API Key 错误或过期 |
| 403 | Forbidden | 没有权限使用该模型 |
| 404 | Model not found | 模型名称错误 |
| 429 | Rate limit exceeded | 请求频率过高 |
| 500 | Internal server error | 服务器问题，稍后重试 |

## 🔧 手动测试 API

### 使用 curl 测试文本生成

```bash
curl -X POST 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation' \
-H 'Authorization: Bearer YOUR_API_KEY' \
-H 'Content-Type: application/json' \
-d '{
  "model": "qwen-turbo",
  "input": {
    "messages": [
      {
        "role": "user",
        "content": "你好，这是一个测试。"
      }
    ]
  },
  "parameters": {
    "temperature": 0.7,
    "max_tokens": 100
  }
}'
```

### 使用 curl 测试图像生成

```bash
curl -X POST 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis' \
-H 'Authorization: Bearer YOUR_API_KEY' \
-H 'Content-Type: application/json' \
-d '{
  "model": "wanx-v1",
  "input": {
    "prompt": "a beautiful landscape"
  },
  "parameters": {
    "size": "1024*1024",
    "n": 1
  }
}'
```

## 🎯 推荐的稳定配置

基于我的测试和文档研究，推荐以下稳定配置：

### 文本生成
- **首选**: `qwen-turbo` (稳定、快速、成本低)
- **备选**: `qwen-plus` (性能更好)
- **避免**: `qwen2.5-omni-7b` (可能需要特殊权限)

### 图像生成
- **首选**: `wanx-v1` (稳定可靠)
- **备选**: `wanx-lite` (速度快)
- **避免**: `wanx2.1-*` 系列 (可能需要特殊权限)

### 语音合成
- **首选**: `qwen-tts-v1` (标准版本)
- **音色**: `zhichu` (知初女声，适合故事)

## 🚀 快速修复步骤

### 1. 重新获取 API Key
1. 登录 [DashScope 控制台](https://dashscope.console.aliyun.com/)
2. 删除旧的 API Key
3. 创建新的 API Key
4. 在应用中更新配置

### 2. 使用基础模型
1. 文本模型选择 `qwen-turbo`
2. 图像模型选择 `wanx-v1`
3. 避免使用实验性模型

### 3. 检查网络
1. 确保能访问 `dashscope.aliyuncs.com`
2. 检查防火墙设置
3. 尝试使用手机热点测试

### 4. 逐步测试
1. 先测试文本生成
2. 再测试图像生成
3. 最后测试语音合成

## 📞 获取帮助

### 官方支持
- **文档**: [DashScope 官方文档](https://help.aliyun.com/zh/model-studio/)
- **控制台**: [DashScope 控制台](https://dashscope.console.aliyun.com/)
- **工单**: 阿里云工单系统

### 社区支持
- **开发者论坛**: 阿里云开发者社区
- **GitHub**: 相关开源项目
- **技术群**: 阿里云技术交流群

## 🔍 调试技巧

### 1. 启用详细日志
在浏览器开发者工具中查看：
- Network 标签页 - 查看 API 请求
- Console 标签页 - 查看错误信息

### 2. 使用测试工具
- Postman 或类似工具测试 API
- 浏览器直接访问测试页面

### 3. 分步骤排查
1. 先确认 API Key 有效
2. 再测试简单的文本生成
3. 最后测试复杂功能

## ✅ 成功连接的标志

当配置正确时，您应该看到：
- ✅ 测试连接显示"已连接"
- ✅ 能够生成文本内容
- ✅ 能够生成图像
- ✅ 能够合成语音

如果仍然有问题，请：
1. 检查 API Key 是否正确
2. 确认已开通相应服务
3. 使用推荐的稳定模型配置
4. 查看浏览器控制台的详细错误信息

---

**记住：使用稳定的基础模型通常比使用最新的实验性模型更可靠！** 🎯
