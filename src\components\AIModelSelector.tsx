
import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react';

interface AIModelSelectorProps {
  selectedModel: string;
  onModelChange: (model: string) => void;
}

const AIModelSelector = ({ selectedModel, onModelChange }: AIModelSelectorProps) => {
  const models = [
    {
      id: 'gemini-2.0-flash-exp',
      name: 'Gemini 2.0 Flash',
      provider: 'Google AI',
      icon: <Sparkles className="w-4 h-4" />,
      description: '最新实验性多模态模型'
    },
    {
      id: 'gemini-1.5-flash',
      name: 'Gemini 1.5 Flash',
      provider: 'Google AI',
      icon: <Zap className="w-4 h-4" />,
      description: '快速响应模型'
    },
    {
      id: 'gemini-1.5-pro',
      name: 'Gemini 1.5 Pro',
      provider: 'Google AI',
      icon: <Brain className="w-4 h-4" />,
      description: '高性能推理模型'
    },
    {
      id: 'anthropic/claude-3.5-sonnet',
      name: 'Claude 3.5 Sonnet',
      provider: 'OpenRouter',
      icon: <Bot className="w-4 h-4" />,
      description: '最新Claude模型'
    },
    {
      id: 'anthropic/claude-3.5-haiku',
      name: 'Claude 3.5 Haiku',
      provider: 'OpenRouter',
      icon: <Zap className="w-4 h-4" />,
      description: '快速轻量模型'
    },
    {
      id: 'openai/gpt-4o',
      name: 'GPT-4o',
      provider: 'OpenRouter',
      icon: <Brain className="w-4 h-4" />,
      description: '多模态GPT-4'
    },
    {
      id: 'openai/gpt-4o-mini',
      name: 'GPT-4o Mini',
      provider: 'OpenRouter',
      icon: <Bot className="w-4 h-4" />,
      description: '轻量级GPT-4o'
    },
    {
      id: 'google/gemini-2.0-flash-exp:free',
      name: 'Gemini 2.0 Flash (Free)',
      provider: 'OpenRouter',
      icon: <Sparkles className="w-4 h-4" />,
      description: '免费版本'
    },
    {
      id: 'meta-llama/llama-3.1-70b-instruct:free',
      name: 'Llama 3.1 70B',
      provider: 'OpenRouter',
      icon: <Bot className="w-4 h-4" />,
      description: '免费开源大模型'
    }
  ];

  const selectedModelInfo = models.find(m => m.id === selectedModel);

  return (
    <div className="flex items-center space-x-2">
      <Select value={selectedModel} onValueChange={onModelChange}>
        <SelectTrigger className="w-48">
          <SelectValue>
            <div className="flex items-center space-x-2">
              {selectedModelInfo?.icon}
              <span className="text-sm">{selectedModelInfo?.name}</span>
            </div>
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {models.map((model) => (
            <SelectItem key={model.id} value={model.id}>
              <div className="flex flex-col">
                <div className="flex items-center space-x-2">
                  {model.icon}
                  <span className="font-medium">{model.name}</span>
                  <span className="text-xs text-gray-500">({model.provider})</span>
                </div>
                <span className="text-xs text-gray-400">{model.description}</span>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default AIModelSelector;
