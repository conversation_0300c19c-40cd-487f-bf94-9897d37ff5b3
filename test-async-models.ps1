# Test Async DashScope Models
# Usage: .\test-async-models.ps1 -Api<PERSON>ey "your-api-key"

param(
    [Parameter(Mandatory=$true)]
    [string]$ApiKey
)

Write-Host "=== Testing Async DashScope Models ===" -ForegroundColor Cyan
Write-Host "API Key: $($ApiKey.Substring(0,10))..." -ForegroundColor Yellow

# Test Async Image Generation
function Test-AsyncImageGeneration {
    param([string]$ApiKey)
    
    Write-Host "`n[ASYNC IMAGE] Testing async image generation..." -ForegroundColor Green
    
    $headers = @{
        'Authorization' = "Bearer $ApiKey"
        'Content-Type' = 'application/json'
        'X-DashScope-Async' = 'enable'
    }

    # Try FLUX first
    $models = @("flux-dev", "flux-schnell", "wanx-v1")
    
    foreach ($model in $models) {
        Write-Host "Trying model: $model" -ForegroundColor Yellow
        
        $body = @{
            model = $model
            input = @{
                prompt = "a cute cat in a garden, high quality"
            }
            parameters = @{
                size = "1024*1024"
                n = 1
            }
        } | ConvertTo-Json -Depth 10

        try {
            $response = Invoke-RestMethod -Uri 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis' -Method POST -Headers $headers -Body $body -TimeoutSec 30
            
            if ($response.output -and $response.output.task_id) {
                Write-Host "SUCCESS: Async task created!" -ForegroundColor Green
                Write-Host "Task ID: $($response.output.task_id)" -ForegroundColor White
                Write-Host "Status: $($response.output.task_status)" -ForegroundColor White
                
                # Try to get result
                $taskId = $response.output.task_id
                $resultUrl = "https://dashscope.aliyuncs.com/api/v1/tasks/$taskId"
                
                Write-Host "Checking task status..." -ForegroundColor Yellow
                Start-Sleep -Seconds 3
                
                for ($i = 0; $i -lt 10; $i++) {
                    try {
                        $result = Invoke-RestMethod -Uri $resultUrl -Method GET -Headers @{'Authorization' = "Bearer $ApiKey"} -TimeoutSec 15
                        
                        if ($result.output.task_status -eq "SUCCEEDED") {
                            Write-Host "SUCCESS: Image generated!" -ForegroundColor Green
                            if ($result.output.results) {
                                Write-Host "Image URL: $($result.output.results[0].url)" -ForegroundColor White
                            }
                            return @{ success = $true; model = $model }
                        } elseif ($result.output.task_status -eq "FAILED") {
                            Write-Host "Task failed: $($result.output.message)" -ForegroundColor Red
                            break
                        } else {
                            Write-Host "Status: $($result.output.task_status), waiting..." -ForegroundColor Yellow
                            Start-Sleep -Seconds 5
                        }
                    } catch {
                        Write-Host "Error checking status: $($_.Exception.Message)" -ForegroundColor Red
                        break
                    }
                }
                
                return @{ success = $true; model = $model; async = $true }
            }
        } catch {
            Write-Host "$model failed: $($_.Exception.Message)" -ForegroundColor Red
            if ($_.ErrorDetails.Message) {
                $errorDetail = $_.ErrorDetails.Message | ConvertFrom-Json
                Write-Host "Error: $($errorDetail.code) - $($errorDetail.message)" -ForegroundColor Yellow
            }
        }
    }
    
    return @{ success = $false }
}

# Test Sync Text Generation (should work)
function Test-SyncText {
    param([string]$ApiKey)
    
    Write-Host "`n[SYNC TEXT] Testing sync text generation..." -ForegroundColor Green
    
    $headers = @{
        'Authorization' = "Bearer $ApiKey"
        'Content-Type' = 'application/json'
    }

    $body = @{
        model = "qwen-turbo"
        input = @{
            messages = @(
                @{
                    role = "user"
                    content = "Write a very short story about a cat in 50 words."
                }
            )
        }
        parameters = @{
            temperature = 0.7
            max_tokens = 100
        }
    } | ConvertTo-Json -Depth 10

    try {
        $response = Invoke-RestMethod -Uri 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation' -Method POST -Headers $headers -Body $body -TimeoutSec 20
        
        $content = $response.output.text
        if ($content) {
            Write-Host "SUCCESS: Text generation works!" -ForegroundColor Green
            Write-Host "Story: $($content.Substring(0, [Math]::Min(100, $content.Length)))..." -ForegroundColor White
            return $true
        }
    } catch {
        Write-Host "FAILED: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.ErrorDetails.Message) {
            $errorDetail = $_.ErrorDetails.Message | ConvertFrom-Json
            Write-Host "Error: $($errorDetail.code) - $($errorDetail.message)" -ForegroundColor Yellow
        }
    }
    
    return $false
}

# Test Async Audio Generation
function Test-AsyncAudio {
    param([string]$ApiKey)
    
    Write-Host "`n[ASYNC AUDIO] Testing async audio generation..." -ForegroundColor Green
    
    $headers = @{
        'Authorization' = "Bearer $ApiKey"
        'Content-Type' = 'application/json'
        'X-DashScope-Async' = 'enable'
    }

    $body = @{
        model = "qwen-tts"
        text = "Hello, this is a test of Qwen TTS."
        voice = "Cherry"
    } | ConvertTo-Json -Depth 10

    try {
        $response = Invoke-RestMethod -Uri 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text2speech/speech-synthesis' -Method POST -Headers $headers -Body $body -TimeoutSec 30
        
        if ($response.output -and $response.output.task_id) {
            Write-Host "SUCCESS: Async audio task created!" -ForegroundColor Green
            Write-Host "Task ID: $($response.output.task_id)" -ForegroundColor White
            return $true
        }
    } catch {
        Write-Host "FAILED: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.ErrorDetails.Message) {
            $errorDetail = $_.ErrorDetails.Message | ConvertFrom-Json
            Write-Host "Error: $($errorDetail.code) - $($errorDetail.message)" -ForegroundColor Yellow
        }
    }
    
    return $false
}

# Run Tests
Write-Host "`nRunning async tests..." -ForegroundColor Cyan

$results = @{
    SyncText = Test-SyncText -ApiKey $ApiKey
    AsyncImage = Test-AsyncImageGeneration -ApiKey $ApiKey
    AsyncAudio = Test-AsyncAudio -ApiKey $ApiKey
}

# Results Summary
Write-Host "`n=== Test Results ===" -ForegroundColor Cyan
Write-Host "Sync Text Generation: $(if($results.SyncText){'✅ WORKS'}else{'❌ FAILED'})" -ForegroundColor $(if($results.SyncText){'Green'}else{'Red'})
Write-Host "Async Image Generation: $(if($results.AsyncImage.success){'✅ WORKS'}else{'❌ FAILED'})" -ForegroundColor $(if($results.AsyncImage.success){'Green'}else{'Red'})
Write-Host "Async Audio Generation: $(if($results.AsyncAudio){'✅ WORKS'}else{'❌ FAILED'})" -ForegroundColor $(if($results.AsyncAudio){'Green'}else{'Red'})

if ($results.AsyncImage.success -and $results.AsyncImage.model) {
    Write-Host "Working Image Model: $($results.AsyncImage.model)" -ForegroundColor Green
}

$workingCount = ($results.Values | Where-Object { $_ -eq $true -or ($_ -is [hashtable] -and $_.success) }).Count
Write-Host "`nWorking Services: $workingCount/3" -ForegroundColor $(if($workingCount -gt 1){'Green'}elseif($workingCount -gt 0){'Yellow'}else{'Red'})

Write-Host "`n=== Configuration Needed ===" -ForegroundColor Cyan
Write-Host "🔧 Your account requires ASYNC API calls" -ForegroundColor Yellow
Write-Host "🔧 Update your app to use async endpoints" -ForegroundColor Yellow
Write-Host "🔧 Implement task polling for image/audio generation" -ForegroundColor Yellow

Write-Host "`nTest completed!" -ForegroundColor Cyan
