<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试更新后的阿里云服务</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .loading { background-color: #fff3cd; border-color: #ffeaa7; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            background-color: #f8f9fa;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 测试更新后的阿里云服务</h1>
        
        <div class="test-section">
            <h3>🔑 API 配置</h3>
            <input type="text" id="apiKey" placeholder="请输入您的 DashScope API Key" value="sk-3207773e69984ecf9ca3d452fa6d4661">
            <button onclick="configureAPI()">配置 API</button>
            <div id="configResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📝 文本生成测试</h3>
            <input type="text" id="textPrompt" placeholder="输入测试提示词" value="Write a short story about a cat">
            <button onclick="testText()" id="textBtn">测试文本生成</button>
            <div id="textResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🖼️ 图像生成测试</h3>
            <input type="text" id="imagePrompt" placeholder="输入图像描述" value="a cute cat in a garden">
            <button onclick="testImage()" id="imageBtn">测试图像生成</button>
            <div id="imageResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔊 语音合成测试</h3>
            <input type="text" id="audioText" placeholder="输入要合成的文本" value="Hello, this is a test">
            <select id="voiceSelect">
                <option value="Cherry">Cherry (女声)</option>
                <option value="Serena">Serena (女声)</option>
                <option value="Ethan">Ethan (男声)</option>
                <option value="Chelsie">Chelsie (女声)</option>
            </select>
            <button onclick="testAudio()" id="audioBtn">测试语音合成</button>
            <div id="audioResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📊 综合测试结果</h3>
            <button onclick="runAllTests()">运行所有测试</button>
            <div id="summaryResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script type="module">
        // 模拟阿里云服务类
        class TestAliCloudService {
            constructor() {
                this.baseUrl = 'https://dashscope.aliyuncs.com/api/v1';
                this.config = null;
            }

            configure(apiKey) {
                this.config = { apiKey };
            }

            async makeRequest(endpoint, data, options = {}) {
                if (!this.config) {
                    throw new Error('API 未配置');
                }

                console.log('发送请求:', endpoint, data);

                try {
                    const response = await fetch(`${this.baseUrl}${endpoint}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${this.config.apiKey}`,
                            ...options.headers
                        },
                        body: JSON.stringify(data)
                    });

                    console.log('响应状态:', response.status);

                    if (!response.ok) {
                        const errorText = await response.text();
                        console.error('API 错误响应:', errorText);
                        throw new Error(`API 错误 (${response.status}): ${errorText}`);
                    }

                    const result = await response.json();
                    console.log('API 响应:', result);
                    return result;
                } catch (error) {
                    console.error('请求失败:', error);
                    throw error;
                }
            }

            async generateText(prompt) {
                const data = {
                    model: 'qwen-turbo',
                    input: {
                        messages: [{ role: 'user', content: prompt }]
                    },
                    parameters: {
                        temperature: 0.7,
                        max_tokens: 200
                    }
                };

                const result = await this.makeRequest('/services/aigc/text-generation/generation', data);
                return result.output?.text || result.output?.choices?.[0]?.message?.content || '';
            }

            async generateImage(prompt) {
                const data = {
                    model: 'flux-dev',
                    input: { prompt },
                    parameters: {
                        size: '1024*1024',
                        n: 1
                    }
                };

                const result = await this.makeRequest('/services/aigc/text2image/image-synthesis', data);
                return result.output?.results?.[0]?.url;
            }

            async generateAudio(text, voice = 'Cherry') {
                const data = {
                    model: 'qwen-tts',
                    text,
                    voice
                };

                const result = await this.makeRequest('/services/aigc/text2speech/speech-synthesis', data);
                return result.output?.audio?.url;
            }
        }

        // 创建服务实例
        const aliService = new TestAliCloudService();

        // 全局函数
        window.configureAPI = function() {
            const apiKey = document.getElementById('apiKey').value;
            const resultDiv = document.getElementById('configResult');
            
            if (!apiKey) {
                resultDiv.innerHTML = '❌ 请输入 API Key';
                resultDiv.className = 'result error';
                resultDiv.style.display = 'block';
                return;
            }

            aliService.configure(apiKey);
            resultDiv.innerHTML = '✅ API 配置成功';
            resultDiv.className = 'result success';
            resultDiv.style.display = 'block';
        };

        window.testText = async function() {
            const btn = document.getElementById('textBtn');
            const resultDiv = document.getElementById('textResult');
            const prompt = document.getElementById('textPrompt').value;

            btn.disabled = true;
            btn.textContent = '测试中...';
            resultDiv.innerHTML = '🔄 正在生成文本...';
            resultDiv.className = 'result loading';
            resultDiv.style.display = 'block';

            try {
                const result = await aliService.generateText(prompt);
                resultDiv.innerHTML = `✅ 文本生成成功！<br><strong>结果:</strong> ${result}`;
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.innerHTML = `❌ 文本生成失败: ${error.message}`;
                resultDiv.className = 'result error';
            } finally {
                btn.disabled = false;
                btn.textContent = '测试文本生成';
            }
        };

        window.testImage = async function() {
            const btn = document.getElementById('imageBtn');
            const resultDiv = document.getElementById('imageResult');
            const prompt = document.getElementById('imagePrompt').value;

            btn.disabled = true;
            btn.textContent = '测试中...';
            resultDiv.innerHTML = '🔄 正在生成图像...';
            resultDiv.className = 'result loading';
            resultDiv.style.display = 'block';

            try {
                const imageUrl = await aliService.generateImage(prompt);
                resultDiv.innerHTML = `✅ 图像生成成功！<br><img src="${imageUrl}" style="max-width: 300px; border-radius: 5px;">`;
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.innerHTML = `❌ 图像生成失败: ${error.message}`;
                resultDiv.className = 'result error';
            } finally {
                btn.disabled = false;
                btn.textContent = '测试图像生成';
            }
        };

        window.testAudio = async function() {
            const btn = document.getElementById('audioBtn');
            const resultDiv = document.getElementById('audioResult');
            const text = document.getElementById('audioText').value;
            const voice = document.getElementById('voiceSelect').value;

            btn.disabled = true;
            btn.textContent = '测试中...';
            resultDiv.innerHTML = '🔄 正在合成语音...';
            resultDiv.className = 'result loading';
            resultDiv.style.display = 'block';

            try {
                const audioUrl = await aliService.generateAudio(text, voice);
                resultDiv.innerHTML = `✅ 语音合成成功！<br><audio controls src="${audioUrl}"></audio>`;
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.innerHTML = `❌ 语音合成失败: ${error.message}`;
                resultDiv.className = 'result error';
            } finally {
                btn.disabled = false;
                btn.textContent = '测试语音合成';
            }
        };

        window.runAllTests = async function() {
            const resultDiv = document.getElementById('summaryResult');
            resultDiv.innerHTML = '🔄 正在运行所有测试...';
            resultDiv.className = 'result loading';
            resultDiv.style.display = 'block';

            const results = {
                text: false,
                image: false,
                audio: false
            };

            // 测试文本
            try {
                await aliService.generateText('Hello');
                results.text = true;
            } catch (error) {
                console.error('文本测试失败:', error);
            }

            // 测试图像
            try {
                await aliService.generateImage('test image');
                results.image = true;
            } catch (error) {
                console.error('图像测试失败:', error);
            }

            // 测试语音
            try {
                await aliService.generateAudio('Hello', 'Cherry');
                results.audio = true;
            } catch (error) {
                console.error('语音测试失败:', error);
            }

            const workingCount = Object.values(results).filter(Boolean).length;
            const summary = `
                <h4>测试结果汇总</h4>
                <p>文本生成: ${results.text ? '✅ 正常' : '❌ 失败'}</p>
                <p>图像生成: ${results.image ? '✅ 正常' : '❌ 失败'}</p>
                <p>语音合成: ${results.audio ? '✅ 正常' : '❌ 失败'}</p>
                <p><strong>工作服务: ${workingCount}/3</strong></p>
            `;

            resultDiv.innerHTML = summary;
            resultDiv.className = workingCount > 1 ? 'result success' : 'result error';
        };

        // 自动配置 API
        if (document.getElementById('apiKey').value) {
            configureAPI();
        }
    </script>
</body>
</html>
