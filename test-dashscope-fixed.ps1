# DashScope API Test Script (Fixed Version)
# Usage: .\test-dashscope-fixed.ps1 -ApiKey "your-api-key-here"

param(
    [Parameter(Mandatory=$true)]
    [string]$ApiKey
)

Write-Host "=== DashScope API Connection Test (Fixed) ===" -ForegroundColor Cyan
Write-Host "API Key: $($ApiKey.Substring(0,10))..." -ForegroundColor Yellow

# Test Text Generation (Fixed)
function Test-TextGeneration {
    param([string]$ApiKey)
    
    Write-Host "`n[TEXT] Testing text generation..." -ForegroundColor Green
    
    $headers = @{
        'Authorization' = "Bearer $ApiKey"
        'Content-Type' = 'application/json'
    }

    $body = @{
        model = "qwen-turbo"
        input = @{
            messages = @(
                @{
                    role = "user"
                    content = "Hello, please introduce yourself briefly."
                }
            )
        }
        parameters = @{
            temperature = 0.7
            max_tokens = 100
        }
    } | ConvertTo-Json -Depth 10

    try {
        $response = Invoke-RestMethod -Uri 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation' -Method POST -Headers $headers -Body $body -TimeoutSec 30
        
        # Fixed: Check for both response formats
        $content = $null
        if ($response.output.text) {
            $content = $response.output.text
        } elseif ($response.output.choices) {
            $content = $response.output.choices[0].message.content
        }
        
        if ($content) {
            Write-Host "SUCCESS: Text generation works!" -ForegroundColor Green
            Write-Host "Response: $content" -ForegroundColor White
            return $true
        } else {
            Write-Host "FAILED: No content in response" -ForegroundColor Red
            Write-Host "Full response: $($response | ConvertTo-Json -Depth 5)" -ForegroundColor Yellow
            return $false
        }
    } catch {
        Write-Host "FAILED: Text generation error!" -ForegroundColor Red
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
        
        if ($_.ErrorDetails.Message) {
            try {
                $errorDetail = $_.ErrorDetails.Message | ConvertFrom-Json
                Write-Host "Error Code: $($errorDetail.code)" -ForegroundColor Yellow
                Write-Host "Error Message: $($errorDetail.message)" -ForegroundColor Yellow
            } catch {
                Write-Host "Error Details: $($_.ErrorDetails.Message)" -ForegroundColor Yellow
            }
        }
        return $false
    }
}

# Test Image Generation (Skip if no permission)
function Test-ImageGeneration {
    param([string]$ApiKey)
    
    Write-Host "`n[IMAGE] Testing image generation..." -ForegroundColor Green
    Write-Host "Note: This may fail if you haven't enabled image generation service" -ForegroundColor Yellow
    
    $headers = @{
        'Authorization' = "Bearer $ApiKey"
        'Content-Type' = 'application/json'
    }

    $body = @{
        model = "wanx-v1"
        input = @{
            prompt = "a simple test image"
        }
        parameters = @{
            size = "1024*1024"
            n = 1
        }
    } | ConvertTo-Json -Depth 10

    try {
        $response = Invoke-RestMethod -Uri 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis' -Method POST -Headers $headers -Body $body -TimeoutSec 60
        
        if ($response.output -and $response.output.results) {
            Write-Host "SUCCESS: Image generation works!" -ForegroundColor Green
            Write-Host "Image URL: $($response.output.results[0].url)" -ForegroundColor White
            return $true
        } else {
            Write-Host "FAILED: Invalid response format" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "FAILED: Image generation not available" -ForegroundColor Yellow
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Yellow
        
        if ($_.ErrorDetails.Message) {
            try {
                $errorDetail = $_.ErrorDetails.Message | ConvertFrom-Json
                Write-Host "Error Code: $($errorDetail.code)" -ForegroundColor Yellow
                Write-Host "Error Message: $($errorDetail.message)" -ForegroundColor Yellow
                
                if ($errorDetail.code -eq "AccessDenied") {
                    Write-Host "SOLUTION: Please enable image generation service in DashScope console" -ForegroundColor Cyan
                }
            } catch {
                Write-Host "Error Details: $($_.ErrorDetails.Message)" -ForegroundColor Yellow
            }
        }
        return $false
    }
}

# Test Audio Generation (Fixed model name)
function Test-AudioGeneration {
    param([string]$ApiKey)
    
    Write-Host "`n[AUDIO] Testing audio generation..." -ForegroundColor Green
    
    $headers = @{
        'Authorization' = "Bearer $ApiKey"
        'Content-Type' = 'application/json'
    }

    # Try different TTS models
    $models = @("cosyvoice-v1", "sambert-zhichu-v1", "sambert-zhixia-v1")
    
    foreach ($model in $models) {
        Write-Host "Trying model: $model" -ForegroundColor Yellow
        
        $body = @{
            model = $model
            input = @{
                text = "Hello, this is a test."
            }
            parameters = @{
                voice = "zhichu"
                format = "mp3"
            }
        } | ConvertTo-Json -Depth 10

        try {
            $response = Invoke-RestMethod -Uri 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text2speech/speech-synthesis' -Method POST -Headers $headers -Body $body -TimeoutSec 30
            
            if ($response.output -and $response.output.audio_url) {
                Write-Host "SUCCESS: Audio generation works with model $model!" -ForegroundColor Green
                Write-Host "Audio URL: $($response.output.audio_url)" -ForegroundColor White
                return $true
            }
        } catch {
            Write-Host "Model $model failed: $($_.Exception.Message)" -ForegroundColor Red
            
            if ($_.ErrorDetails.Message) {
                try {
                    $errorDetail = $_.ErrorDetails.Message | ConvertFrom-Json
                    Write-Host "Error Code: $($errorDetail.code)" -ForegroundColor Yellow
                } catch {
                    # Continue to next model
                }
            }
        }
    }
    
    Write-Host "FAILED: All audio models failed" -ForegroundColor Red
    Write-Host "SOLUTION: Please check if TTS service is enabled in DashScope console" -ForegroundColor Cyan
    return $false
}

# Main Test Process
Write-Host "`nStarting DashScope API connection test..." -ForegroundColor Cyan

$results = @{
    Text = Test-TextGeneration -ApiKey $ApiKey
    Image = Test-ImageGeneration -ApiKey $ApiKey
    Audio = Test-AudioGeneration -ApiKey $ApiKey
}

# Output Test Results
Write-Host "`n=== Test Results Summary ===" -ForegroundColor Cyan
Write-Host "Text Generation: $(if($results.Text){'SUCCESS'}else{'FAILED'})" -ForegroundColor $(if($results.Text){'Green'}else{'Red'})
Write-Host "Image Generation: $(if($results.Image){'SUCCESS'}else{'FAILED'})" -ForegroundColor $(if($results.Image){'Green'}else{'Red'})
Write-Host "Audio Generation: $(if($results.Audio){'SUCCESS'}else{'FAILED'})" -ForegroundColor $(if($results.Audio){'Green'}else{'Red'})

$successCount = ($results.Values | Where-Object {$_}).Count
Write-Host "`nOverall Result: $successCount/3 tests passed" -ForegroundColor $(if($successCount -eq 3){'Green'}elseif($successCount -gt 0){'Yellow'}else{'Red'})

# Recommendations based on results
Write-Host "`n=== Recommendations ===" -ForegroundColor Cyan

if ($results.Text) {
    Write-Host "✅ TEXT: You can use text generation in your app!" -ForegroundColor Green
    Write-Host "   Recommended model: qwen-turbo" -ForegroundColor White
} else {
    Write-Host "❌ TEXT: Check your API key and network connection" -ForegroundColor Red
}

if ($results.Image) {
    Write-Host "✅ IMAGE: You can use image generation in your app!" -ForegroundColor Green
    Write-Host "   Recommended model: wanx-v1" -ForegroundColor White
} else {
    Write-Host "⚠️  IMAGE: Enable image generation service in DashScope console" -ForegroundColor Yellow
    Write-Host "   Or disable image generation in your app for now" -ForegroundColor White
}

if ($results.Audio) {
    Write-Host "✅ AUDIO: You can use audio generation in your app!" -ForegroundColor Green
} else {
    Write-Host "⚠️  AUDIO: Enable TTS service in DashScope console" -ForegroundColor Yellow
    Write-Host "   Or use browser TTS as fallback" -ForegroundColor White
}

Write-Host "`nNext Steps:" -ForegroundColor Cyan
if ($results.Text) {
    Write-Host "1. Configure your app with the working services" -ForegroundColor White
    Write-Host "2. For failed services, check DashScope console to enable them" -ForegroundColor White
    Write-Host "3. Use fallback options (browser TTS) for unavailable services" -ForegroundColor White
} else {
    Write-Host "1. Double-check your API key in DashScope console" -ForegroundColor White
    Write-Host "2. Ensure you have enabled the required services" -ForegroundColor White
    Write-Host "3. Check your network connection" -ForegroundColor White
}

Write-Host "`nTest completed!" -ForegroundColor Cyan
