
import React, { useEffect, useState } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { toast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Volume2, Loader2, Bookmark, Check, BookUser, Quote, Type, MessageSquareText, Footprints } from 'lucide-react';
import { Skeleton } from './ui/skeleton';
import { Badge } from './ui/badge';
import { Separator } from './ui/separator';

interface WordDefinition {
    word: string;
    definition: string;
    partOfSpeech: string;
    example: string;
    phonetic: string;
    chineseDefinition: string;
    chineseExample: string;
    baseForm: string;
}

interface WordCardProps {
    word: string;
    context: string;
    isOpen: boolean;
    onClose: () => void;
    playTextToSpeech: (text: string) => void;
    isTtsLoading?: boolean;
}

const DefinitionSkeleton = () => (
    <div className="space-y-4 py-4">
        <Skeleton className="h-4 w-1/2" />
        <Skeleton className="h-4 w-full" />
        <div className="space-y-2 pt-4">
            <Skeleton className="h-4 w-1/4" />
            <Skeleton className="h-4 w-4/5" />
            <Skeleton className="h-4 w-3/5" />
        </div>
    </div>
);

const WordCard = ({ word, context, isOpen, onClose, playTextToSpeech, isTtsLoading = false }: WordCardProps) => {
    const { user } = useAuth();
    const queryClient = useQueryClient();
    const [isAdding, setIsAdding] = useState(false);
    const [isAlreadyInVocab, setIsAlreadyInVocab] = useState(false);

    const { data: definition, isLoading: isDefinitionLoading, error: definitionError } = useQuery<WordDefinition>({
        queryKey: ['definition', word, context],
        queryFn: async () => {
            const { data, error } = await supabase.functions.invoke('get-word-definition', {
                body: { word, context },
            });
            if (error) throw new Error(error.message);
            return data;
        },
        enabled: !!word && !!context && isOpen,
        staleTime: Infinity,
    });

    useEffect(() => {
        const checkIfInVocab = async () => {
            if (!user || !word || !isOpen) {
                setIsAlreadyInVocab(false);
                return;
            }

            const lookupWord = definition?.baseForm || word;
            if (!lookupWord) return;

            const { data } = await supabase
                .from('vocabulary')
                .select('id')
                .eq('user_id', user.id)
                .ilike('word', lookupWord)
                .maybeSingle();

            setIsAlreadyInVocab(!!data);
        };
        checkIfInVocab();
    }, [word, definition?.baseForm, user, isOpen]);

    const handleAddToVocabulary = async () => {
        if (!user) {
            toast({
                variant: 'destructive',
                title: '请先登录',
                description: '登录后才能将单词添加到生词本。',
            });
            return;
        }
        if (!definition) return;
        setIsAdding(true);
        const { error } = await supabase.from('vocabulary').insert({
            user_id: user.id,
            word: definition.baseForm || word,
            context,
            definition: definition as any,
        });

        if (error) {
            toast({ variant: 'destructive', title: '添加失败', description: error.message.includes('duplicate key') ? '生词本里已经有这个单词了' : error.message });
            if (error.message.includes('duplicate key')) {
                setIsAlreadyInVocab(true);
            }
        } else {
            toast({ title: '添加成功', description: `“${word}”已添加到你的生词本` });
            setIsAlreadyInVocab(true);
            queryClient.invalidateQueries({ queryKey: ['vocabulary'] });
        }
        setIsAdding(false);
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle className="text-3xl font-bold flex items-center justify-between">
                        <span className="flex items-center gap-3">
                            {word}
                            <Button variant="ghost" size="icon" onClick={() => playTextToSpeech(word)} disabled={isTtsLoading}>
                                {isTtsLoading ? <Loader2 className="h-5 w-5 animate-spin" /> : <Volume2 className="h-5 w-5" />}
                            </Button>
                        </span>
                        {definition?.phonetic && (
                           <span className="text-lg text-gray-500 font-normal">[{definition.phonetic}]</span>
                        )}
                    </DialogTitle>
                </DialogHeader>

                <div className="py-2 space-y-4">
                    {isDefinitionLoading ? <DefinitionSkeleton /> : definitionError ? <p className="text-red-500">无法加载释义。</p> : null}
                    {definition && (
                        <div className="space-y-4 text-base">
                            <div className="flex items-center gap-3">
                                <Badge variant="secondary">{definition.partOfSpeech}</Badge>
                                <p className="font-semibold text-lg text-gray-800">{definition.chineseDefinition}</p>
                            </div>
                             
                            <div className="flex items-start gap-3 text-gray-700">
                                <Type className="w-5 h-5 mt-1 text-gray-400 flex-shrink-0" />
                                <p>{definition.definition}</p>
                            </div>
                            
                            <Separator />

                            <div className="space-y-2 text-gray-600">
                                <p className="font-semibold flex items-center gap-2 text-gray-800"><Quote className="w-4 h-4 text-gray-400" /> 例句</p>
                                <div className="pl-6 text-sm space-y-1">
                                    <p>{definition.example}</p>
                                    <p className="text-gray-500">{definition.chineseExample}</p>
                                </div>
                            </div>
                            
                            <div className="space-y-2 text-gray-600">
                                <p className="font-semibold flex items-center gap-2 text-gray-800"><MessageSquareText className="w-4 h-4 text-gray-400" /> 上下文</p>
                                <p className="pl-6 text-sm text-gray-500 italic">"{context}"</p>
                            </div>

                            {definition.baseForm && word.toLowerCase() !== definition.baseForm.toLowerCase() && (
                                <div className="space-y-2 text-gray-600">
                                    <p className="font-semibold flex items-center gap-2 text-gray-800"><Footprints className="w-4 h-4 text-gray-400" /> 基本形态</p>
                                    <p className="pl-6 text-sm">{definition.baseForm}</p>
                                </div>
                            )}
                        </div>
                    )}
                </div>
                
                <DialogFooter className="pt-4">
                    <Button onClick={handleAddToVocabulary} disabled={isAdding || isAlreadyInVocab || isDefinitionLoading} className="w-full">
                        {isAdding ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : isAlreadyInVocab ? <Check className="mr-2 h-4 w-4" /> : <BookUser className="mr-2 h-4 w-4" />}
                        {isAlreadyInVocab ? '已在生词本' : '加入生词本'}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};

export default WordCard;
