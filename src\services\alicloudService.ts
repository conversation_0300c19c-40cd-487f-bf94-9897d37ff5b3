// 阿里云 AI 服务集成
export interface AliCloudConfig {
  apiKey: string;
  region?: string;
}

export interface StoryGenerationResult {
  title: string;
  description: string;
  story: string;
  storyChinese?: string;
  imageUrl?: string;
}

export interface AudioGenerationResult {
  audioBlob: Blob;
  format: string;
  duration?: number;
}

export interface ImageGenerationResult {
  imageUrl: string;
  revisedPrompt?: string;
}

class AliCloudService {
  private config: AliCloudConfig | null = null;
  private baseUrl = 'https://dashscope.aliyuncs.com/api/v1';

  // 设置配置
  setConfig(config: AliCloudConfig) {
    this.config = config;
  }

  // 检查配置是否有效
  isConfigured(): boolean {
    return !!(this.config?.apiKey);
  }

  // 通用请求方法
  private async makeRequest(endpoint: string, data: any, options: any = {}) {
    if (!this.config) {
      throw new Error('阿里云配置未设置');
    }

    // 设置超时时间，图像和语音生成需要更长时间
    const timeout = options.timeout || (endpoint.includes('image') || endpoint.includes('speech') ? 60000 : 30000);

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`,
          'X-DashScope-Async': options.async ? 'enable' : 'disable',
          ...options.headers
        },
        body: JSON.stringify(data),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`阿里云 API 错误 (${response.status}): ${errorText}`);
      }

      const result = await response.json();

      if (result.code && result.code !== '200') {
        throw new Error(`阿里云 API 错误: ${result.message || result.code}`);
      }

      return result;
    } catch (error) {
      clearTimeout(timeoutId);
      if (error.name === 'AbortError') {
        throw new Error(`请求超时 (${timeout}ms)，请检查网络连接或稍后重试`);
      }
      throw error;
    }
  }

  // 异步请求方法 - 用于图像生成V2版
  private async makeAsyncRequest(endpoint: string, data: any): Promise<any> {
    if (!this.config) {
      throw new Error('阿里云配置未设置');
    }

    // 第一步：创建任务
    const createResponse = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`,
        'X-DashScope-Async': 'enable'
      },
      body: JSON.stringify(data),
    });

    if (!createResponse.ok) {
      const errorText = await createResponse.text();
      throw new Error(`阿里云异步任务创建失败 (${createResponse.status}): ${errorText}`);
    }

    const createResult = await createResponse.json();
    if (createResult.code) {
      throw new Error(`阿里云异步任务创建失败: ${createResult.message || createResult.code}`);
    }

    const taskId = createResult.output?.task_id;
    if (!taskId) {
      throw new Error('未获取到任务ID');
    }

    console.log('图像生成任务已创建，任务ID:', taskId);

    // 第二步：轮询任务状态
    const maxAttempts = 60; // 最多等待5分钟
    const pollInterval = 5000; // 每5秒查询一次

    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      await new Promise(resolve => setTimeout(resolve, pollInterval));

      const statusResponse = await fetch(`${this.baseUrl}/tasks/${taskId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
        },
      });

      if (!statusResponse.ok) {
        throw new Error(`任务状态查询失败: ${statusResponse.status}`);
      }

      const statusResult = await statusResponse.json();
      console.log(`任务状态 (${attempt + 1}/${maxAttempts}):`, statusResult.output?.task_status);

      if (statusResult.output?.task_status === 'SUCCEEDED') {
        console.log('图像生成成功！');
        return statusResult;
      } else if (statusResult.output?.task_status === 'FAILED') {
        throw new Error(`任务执行失败: ${statusResult.output?.message || '未知错误'}`);
      }
      // 继续等待 PENDING 或 RUNNING 状态
    }

    throw new Error('图像生成超时，请稍后重试');
  }

  // 文本生成 - 通义千问
  async generateText(prompt: string, model: string = 'qwen-turbo'): Promise<string> {
    const data = {
      model: model,
      input: {
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ]
      },
      parameters: {
        temperature: 0.7,
        max_tokens: 2000,
        top_p: 0.8
      }
    };

    const result = await this.makeRequest('/services/aigc/text-generation/generation', data);
    // 阿里云返回格式: output.text 或 output.choices[0].message.content
    return result.output?.text || result.output?.choices?.[0]?.message?.content || '';
  }

  // 故事生成（多模态）
  async generateStory(prompt: string): Promise<StoryGenerationResult> {
    const storyPrompt = `请根据以下提示创作一个英文故事，并提供中文翻译。同时为这个故事生成一个适合的图像描述：

提示：${prompt}

请按照以下JSON格式返回：
{
  "title": "故事标题（英文）",
  "description": "故事简介（中文，50字以内）",
  "story": "完整的英文故事（200-500词）",
  "storyChinese": "中文翻译",
  "imagePrompt": "图像生成提示词（英文，描述故事场景）"
}

要求：
1. 故事要有趣且适合英语学习
2. 使用简单易懂的英语词汇
3. 包含对话和描述
4. 中文翻译要准确自然
5. 图像提示词要生动具体`;

    try {
      const response = await this.generateText(storyPrompt, 'qwen-plus');
      const parsed = this.parseStoryResponse(response);
      
      // 如果有图像提示词，生成图像
      if (parsed.imagePrompt) {
        try {
          const imageResult = await this.generateImage(parsed.imagePrompt);
          parsed.imageUrl = imageResult.imageUrl;
        } catch (error) {
          console.warn('图像生成失败，继续使用文本故事:', error);
        }
      }
      
      return parsed;
    } catch (error) {
      console.error('故事生成失败:', error);
      throw error;
    }
  }

  // 图像生成 - 通义万相V2版 (推荐)
  async generateImage(prompt: string, model: string = 'wanx2.1-t2i-turbo'): Promise<ImageGenerationResult> {
    // 根据模型选择不同的数据格式
    let data: any;
    let useAsync = false;

    if (model.startsWith('wanx2.')) {
      // 通义万相V2版 - 使用异步调用
      useAsync = true;
      data = {
        model: model,
        input: {
          prompt: prompt,
          negative_prompt: '低分辨率、错误、最差质量、低质量、残缺、多余的手指、比例不良、模糊、变形'
        },
        parameters: {
          size: '1024*1024',
          n: 1,
          prompt_extend: true, // 开启智能改写，提升效果
          watermark: false,    // 不添加水印
          seed: Math.floor(Math.random() * 2147483647) // 随机种子
        }
      };
    } else {
      // FLUX 或其他模型 - 使用同步调用
      data = {
        model: model,
        input: {
          prompt: prompt,
          negative_prompt: 'blurry, low quality, distorted, ugly',
        },
        parameters: {
          size: '1024*1024',
          n: 1,
          seed: Math.floor(Math.random() * 2147483647)
        }
      };
    }

    try {
      const result = useAsync
        ? await this.makeAsyncRequest('/services/aigc/text2image/image-synthesis', data)
        : await this.makeRequest('/services/aigc/text2image/image-synthesis', data);

      if (result.output?.results?.[0]?.url) {
        return {
          imageUrl: result.output.results[0].url,
          revisedPrompt: result.output.results[0].actual_prompt || result.output.results[0].orig_prompt || prompt
        };
      } else {
        throw new Error('图像生成失败：未返回图像URL');
      }
    } catch (error) {
      console.error(`${model} 图像生成失败:`, error);
      throw error;
    }
  }

  // 语音合成 - Qwen-TTS
  async generateAudio(text: string, voice: string = 'Cherry'): Promise<AudioGenerationResult> {
    const data = {
      model: 'qwen-tts',
      text: text,
      voice: voice
    };

    try {
      const result = await this.makeRequest('/services/aigc/text2speech/speech-synthesis', data);

      if (result.output?.audio?.url) {
        // 下载音频文件
        const audioResponse = await fetch(result.output.audio.url);
        if (!audioResponse.ok) {
          throw new Error('音频下载失败');
        }

        const audioBlob = await audioResponse.blob();
        return {
          audioBlob,
          format: 'mp3',
          duration: result.output?.audio?.duration
        };
      } else {
        throw new Error('语音合成失败：未返回音频URL');
      }
    } catch (error) {
      console.error('Qwen-TTS 语音合成失败:', error);
      throw error;
    }
  }

  // 多模态理解 - 通义千问VL
  async analyzeImage(imageUrl: string, question: string): Promise<string> {
    const data = {
      model: 'qwen-vl-plus',
      input: {
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'image',
                image: imageUrl
              },
              {
                type: 'text',
                text: question
              }
            ]
          }
        ]
      }
    };

    const result = await this.makeRequest('/services/aigc/multimodal-generation/generation', data);
    return result.output?.choices?.[0]?.message?.content || '';
  }

  // 获取可用的语音列表 (Qwen-TTS)
  getAvailableVoices(): Array<{id: string, name: string, language: string}> {
    return [
      { id: 'Cherry', name: 'Cherry (女声)', language: 'zh-CN' },
      { id: 'Serena', name: 'Serena (女声)', language: 'zh-CN' },
      { id: 'Ethan', name: 'Ethan (男声)', language: 'en-US' },
      { id: 'Chelsie', name: 'Chelsie (女声)', language: 'en-US' }
    ];
  }

  // 获取可用的文本模型
  getAvailableTextModels(): Array<{id: string, name: string, description: string}> {
    return [
      { id: 'qwen-turbo', name: 'Qwen-Turbo (推荐)', description: '快速响应，适合日常对话' },
      { id: 'qwen-plus', name: 'Qwen-Plus', description: '平衡性能，适合复杂任务' },
      { id: 'qwen-max', name: 'Qwen-Max', description: '最强性能，适合专业创作' },
      { id: 'qwen-max-longcontext', name: 'Qwen-Max-LongContext', description: '长文本处理' },
      { id: 'qwen2.5-omni-7b', name: 'Qwen2.5-Omni (实验)', description: '多模态模型，可能需要特殊权限' }
    ];
  }

  // 获取可用的图像模型
  getAvailableImageModels(): Array<{id: string, name: string, description: string, price: string}> {
    return [
      {
        id: 'wanx2.1-t2i-turbo',
        name: '通义万相V2.1-Turbo (推荐)',
        description: '最新V2版本，速度快、质量高、成本低，专为故事生成优化',
        price: '0.14元/张'
      },
      {
        id: 'wanx2.1-t2i-plus',
        name: '通义万相V2.1-Plus',
        description: '细节更丰富，质量更高，适合精美插图',
        price: '0.20元/张'
      },
      {
        id: 'wanx2.0-t2i-turbo',
        name: '通义万相V2.0-Turbo',
        description: '擅长质感人像与创意设计，性价比高',
        price: '0.04元/张'
      },
      {
        id: 'flux-dev',
        name: 'FLUX Dev',
        description: '国际顶级模型，图像质量极高',
        price: '较高'
      },
      {
        id: 'flux-schnell',
        name: 'FLUX Schnell',
        description: '快速生成，质量优秀',
        price: '中等'
      },
      {
        id: 'wanx-v1',
        name: '通义万相 V1 (传统)',
        description: '稳定可靠的基础图像生成',
        price: '0.16元/张'
      }
    ];
  }

  // 解析故事响应
  private parseStoryResponse(response: string): StoryGenerationResult & { imagePrompt?: string } {
    try {
      // 尝试解析 JSON
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          title: parsed.title || '未命名故事',
          description: parsed.description || '一个有趣的故事',
          story: parsed.story || response,
          storyChinese: parsed.storyChinese,
          imagePrompt: parsed.imagePrompt
        };
      }
    } catch (error) {
      console.warn('JSON 解析失败，使用文本解析:', error);
    }

    // 如果 JSON 解析失败，使用文本解析
    return {
      title: this.extractTitle(response) || '生成的故事',
      description: '一个由通义千问生成的有趣故事',
      story: response,
      storyChinese: undefined
    };
  }

  // 从文本中提取标题
  private extractTitle(text: string): string | null {
    const titlePatterns = [
      /Title:\s*(.+)/i,
      /标题[：:]\s*(.+)/,
      /^(.+)$/m // 第一行作为标题
    ];

    for (const pattern of titlePatterns) {
      const match = text.match(pattern);
      if (match && match[1]) {
        return match[1].trim().substring(0, 50);
      }
    }

    return null;
  }

  // 测试连接
  async testConnection(): Promise<{text: boolean, image: boolean, audio: boolean}> {
    const results = {
      text: false,
      image: false,
      audio: false
    };

    try {
      // 测试文本生成
      const textResponse = await this.generateText('Hello, this is a test.');
      results.text = textResponse.length > 0;
    } catch (error) {
      console.error('文本生成测试失败:', error);
    }

    try {
      // 测试图像生成
      const imageResponse = await this.generateImage('A simple test image');
      results.image = !!imageResponse.imageUrl;
    } catch (error) {
      console.error('图像生成测试失败:', error);
    }

    try {
      // 测试语音合成
      const audioResponse = await this.generateAudio('Hello, this is a test.');
      results.audio = audioResponse.audioBlob.size > 0;
    } catch (error) {
      console.error('语音合成测试失败:', error);
    }

    return results;
  }
}

// 创建单例实例
export const alicloudService = new AliCloudService();

// 导出类型和服务
export default AliCloudService;
