
import React from 'react';
import { Send } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

interface MessageInputProps {
  inputMessage: string;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onSubmit: (e: React.FormEvent) => void;
  loading: boolean;
}

const MessageInput = ({
  inputMessage,
  onInputChange,
  onSubmit,
  loading,
}: MessageInputProps) => {
  return (
    <form onSubmit={onSubmit} className="p-4 border-t">
      <div className="flex space-x-2">
        <Input
          value={inputMessage}
          onChange={onInputChange}
          placeholder="输入你的英语问题或使用语音..."
          disabled={loading}
          className="flex-1"
        />
        <Button type="submit" disabled={loading || !inputMessage.trim()}>
          <Send className="w-4 h-4" />
        </Button>
      </div>
    </form>
  );
};

export default MessageInput;
