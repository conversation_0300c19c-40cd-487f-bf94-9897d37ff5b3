
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { encodeBase64 } from "https://deno.land/std@0.224.0/encoding/base64.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// This is a memory-safe, iterative function to convert a hex string to a byte array.
// It avoids recursion and large intermediate arrays that can cause stack overflows.
function hexToBytes(hex: string): Uint8Array {
  // MiniMax sometimes returns an odd-length hex string.
  // We'll be safe and truncate to an even length if necessary.
  if (hex.length % 2 !== 0) {
    console.warn("Odd-length hex string detected, truncating last character.");
    hex = hex.slice(0, -1);
  }
  const bytes = new Uint8Array(hex.length / 2);
  for (let i = 0; i < bytes.length; i++) {
    const hexIndex = i * 2;
    bytes[i] = parseInt(hex.substring(hexIndex, hexIndex + 2), 16);
  }
  return bytes;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { text } = await req.json()

    if (!text) {
      throw new Error('Text is required')
    }
    
    const minimaxApiKey = Deno.env.get('MINIMAX_API_KEY');
    const minimaxGroupId = Deno.env.get('MINIMAX_GROUP_ID');

    if (!minimaxApiKey || !minimaxGroupId) {
        throw new Error('MiniMax API key or Group ID is not configured. Please set them in your Supabase project secrets.');
    }

    console.log('Calling MiniMax TTS API...');
    
    const response = await fetch(`https://api.minimaxi.com/v1/t2a_v2?GroupId=${minimaxGroupId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${minimaxApiKey}`,
      },
      body: JSON.stringify({
        model: "speech-02-hd",
        text: text,
        stream: false,
        output_format: "hex",
        voice_setting: {
            voice_id: "male-qn-qingse",
            speed: 1,
            vol: 1,
            pitch: 0,
        },
        audio_setting: {
            sample_rate: 32000,
            bitrate: 128000,
            format: "mp3"
        }
      }),
    })

    console.log(`MiniMax TTS API Response Status: ${response.status} ${response.statusText}`);

    if (!response.ok) {
        const errorText = await response.text();
        console.error('MiniMax TTS API Error Response Body:', errorText);
        throw new Error(`Failed to generate speech from MiniMax. Status: ${response.status}`);
    }

    const responseData = await response.json();
    
    if (responseData.base_resp?.status_code !== 0) {
        console.error('MiniMax API returned an error:', JSON.stringify(responseData, null, 2));
        throw new Error(`MiniMax API Error: ${responseData.base_resp.status_msg}`);
    }

    const hexAudio = responseData.data?.audio;
    const format = responseData.extra_info?.audio_format || 'mp3';

    if (!hexAudio) {
      console.error('MiniMax Error: No audio in response', responseData);
      throw new Error('Failed to get audio from MiniMax.');
    }
    
    console.log('Successfully received audio data from MiniMax.');

    // Convert hex audio to base64
    console.log('Converting hex to base64...');
    const audioBytes = hexToBytes(hexAudio);
    const base64Audio = encodeBase64(audioBytes);
    console.log('Conversion complete.');

    return new Response(
      JSON.stringify({ audioContent: base64Audio, format: format }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      },
    )
  } catch (error) {
    console.error('TTS Function Error:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      },
    )
  }
})
