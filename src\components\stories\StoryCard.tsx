
import React, { useState, useRef, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Play, Download, BookOpen, Loader2, Pause } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { Link } from 'react-router-dom';
import { useLocalAudio } from '@/hooks/useLocalAI';
import { useLocalAudioStorage } from '@/hooks/useLocalAudioStorage';

interface Story {
  title: string;
  description: string;
  story: string;
  storyChinese?: string;
  imageUrl?: string;
  id: string;
}

interface StoryCardProps {
  story: Story;
}

const StoryCard: React.FC<StoryCardProps> = ({ story }) => {
  const { toast } = useToast();
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const audioRef = useRef<HTMLAudioElement>(null);
  const { getAudioUrl } = useLocalAudioStorage();
  const { generateAudio, isGenerating } = useLocalAudio();

  // 检查本地是否已有音频
  useEffect(() => {
    const checkLocalAudio = async () => {
      const localUrl = await getAudioUrl(story.id);
      if (localUrl) {
        setAudioUrl(localUrl);
      }
    };
    checkLocalAudio();
  }, [story.id, getAudioUrl]);

  const handlePlay = async (e: React.MouseEvent) => {
    e.preventDefault();

    if (audioUrl && audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
    } else if (story?.story) {
      // 生成音频
      generateAudio({ text: story.story, storyId: story.id });
    }
  };

  const handleDownload = async (e: React.MouseEvent) => {
    e.preventDefault();

    if (audioUrl) {
      const link = document.createElement('a');
      link.href = audioUrl;
      link.download = `${story.title.replace(/\s/g, '_')}.mp3`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: "下载开始",
        description: "音频文件正在下载中...",
      });
    } else {
      toast({
        title: '请先生成音频',
        description: '点击播放按钮来生成音频文件。',
      });
    }
  };

  // 音频事件处理
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const onPlay = () => setIsPlaying(true);
    const onPause = () => setIsPlaying(false);
    const onEnded = () => setIsPlaying(false);

    audio.addEventListener('play', onPlay);
    audio.addEventListener('pause', onPause);
    audio.addEventListener('ended', onEnded);

    return () => {
      audio.removeEventListener('play', onPlay);
      audio.removeEventListener('pause', onPause);
      audio.removeEventListener('ended', onEnded);
    };
  }, [audioUrl]);

  // 设置音频源
  useEffect(() => {
    if (audioUrl && audioRef.current) {
      audioRef.current.src = audioUrl;
    }
  }, [audioUrl]);

  return (
    <>
      <Link to={`/stories/${story.id}`} state={{ story }} className="block">
        <Card className="flex flex-col overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 h-full">
          <CardHeader className="p-0">
            {story.imageUrl ? (
              <img src={story.imageUrl} alt={story.title} className="w-full h-48 object-cover" />
            ) : (
              <div className="w-full h-48 bg-gradient-to-br from-amber-50 to-orange-100 flex items-center justify-center">
                <BookOpen className="w-16 h-16 text-amber-400" />
              </div>
            )}
          </CardHeader>
          <CardContent className="p-6 flex-1">
            <CardTitle className="text-xl font-bold mb-2">{story.title}</CardTitle>
            <p className="text-gray-600 text-sm">{story.description}</p>
            {audioUrl && (
              <div className="mt-2 text-xs text-green-600 flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-1"></span>
                音频已缓存
              </div>
            )}
          </CardContent>
          <CardFooter className="p-6 pt-0 flex justify-end space-x-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={handleDownload}
              disabled={!audioUrl}
              title={audioUrl ? "下载音频" : "请先生成音频"}
            >
              <Download className="h-5 w-5" />
            </Button>
            <Button
              onClick={handlePlay}
              disabled={isGenerating}
              className="bg-gradient-to-r from-orange-500 to-amber-500 text-white"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  生成中...
                </>
              ) : audioUrl ? (
                isPlaying ? (
                  <>
                    <Pause className="mr-2 h-4 w-4" />
                    Pause
                  </>
                ) : (
                  <>
                    <Play className="mr-2 h-4 w-4" />
                    Play
                  </>
                )
              ) : (
                <>
                  <Play className="mr-2 h-4 w-4" />
                  Listen
                </>
              )}
            </Button>
          </CardFooter>
        </Card>
      </Link>
      <audio ref={audioRef} className="hidden" />
    </>
  );
};

export default StoryCard;
