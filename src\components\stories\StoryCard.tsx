
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Play, Download, BookOpen } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { Link } from 'react-router-dom';

interface Story {
  title: string;
  description: string;
  story: string;
  storyChinese?: string;
  imageUrl?: string;
  id: string;
}

interface StoryCardProps {
  story: Story;
}

const StoryCard: React.FC<StoryCardProps> = ({ story }) => {
  const { toast } = useToast();

  const handlePlay = (e: React.MouseEvent) => {
    e.preventDefault();
    toast({
      title: '功能开发中',
      description: '音频播放功能即将上线，敬请期待！',
    });
  };

  const handleDownload = (e: React.MouseEvent) => {
    e.preventDefault();
    toast({
      title: '功能开发中',
      description: '音频下载功能即将上线，敬请期待！',
    });
  };

  return (
    <Link to={`/stories/${story.id}`} state={{ story }} className="block">
      <Card className="flex flex-col overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 h-full">
        <CardHeader className="p-0">
          {story.imageUrl ? (
            <img src={story.imageUrl} alt={story.title} className="w-full h-48 object-cover" />
          ) : (
            <div className="w-full h-48 bg-gradient-to-br from-amber-50 to-orange-100 flex items-center justify-center">
              <BookOpen className="w-16 h-16 text-amber-400" />
            </div>
          )}
        </CardHeader>
        <CardContent className="p-6 flex-1">
          <CardTitle className="text-xl font-bold mb-2">{story.title}</CardTitle>
          <p className="text-gray-600 text-sm">{story.description}</p>
        </CardContent>
        <CardFooter className="p-6 pt-0 flex justify-end space-x-2">
          <Button variant="ghost" size="icon" onClick={handleDownload}>
            <Download className="h-5 w-5" />
          </Button>
          <Button onClick={handlePlay} className="bg-gradient-to-r from-orange-500 to-amber-500 text-white">
            <Play className="mr-2 h-4 w-4" />
            Listen
          </Button>
        </CardFooter>
      </Card>
    </Link>
  );
};

export default StoryCard;
