
import React from 'react';
import { Headphones } from 'lucide-react';

interface Exercise {
  id: string;
  title: string;
  audioUrl: string;
  transcript: string;
  questions: {
    question: string;
    options: string[];
    correct: number;
  }[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
}

interface ExerciseSelectorProps {
  exercises: Exercise[];
  currentExercise: number;
  onExerciseSelect: (index: number) => void;
}

const ExerciseSelector = ({ exercises, currentExercise, onExerciseSelect }: ExerciseSelectorProps) => {
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-blue-100 text-blue-800';
      case 'advanced': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <>
      {/* Header */}
      <div className="bg-white rounded-2xl shadow-lg mb-6 p-6">
        <div className="flex items-center space-x-4">
          <div className="bg-gradient-to-r from-blue-500 to-emerald-500 p-3 rounded-full">
            <Headphones className="text-white" size={24} />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">听力练习</h1>
            <p className="text-gray-600">提升英语听力理解能力</p>
          </div>
        </div>
      </div>

      {/* Exercise Selection */}
      <div className="bg-white rounded-2xl shadow-lg mb-6 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">选择练习</h2>
        <div className="flex space-x-4">
          {exercises.map((exercise, index) => (
            <button
              key={exercise.id}
              onClick={() => onExerciseSelect(index)}
              className={`flex-1 p-4 rounded-xl border-2 transition-all duration-200 ${
                currentExercise === index
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-blue-300'
              }`}
            >
              <div className="text-left">
                <h3 className="font-semibold text-gray-900 mb-2">{exercise.title}</h3>
                <span className={`inline-block px-3 py-1 rounded-full text-xs font-medium ${getDifficultyColor(exercise.difficulty)}`}>
                  {exercise.difficulty === 'beginner' ? '初级' : 
                   exercise.difficulty === 'intermediate' ? '中级' : '高级'}
                </span>
              </div>
            </button>
          ))}
        </div>
      </div>
    </>
  );
};

export default ExerciseSelector;
