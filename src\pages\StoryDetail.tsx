import React, { useState, useRef, useEffect } from 'react';
import { useLocation, Link } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Play, Download, ArrowLeft, Loader2, Pause, BookOpen, Volume2 } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useMutation } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import ClickableText from '@/components/ClickableText';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from '@/components/ui/badge';

interface WordDefinition {
  word: string;
  definition: string;
  partOfSpeech: string;
  example: string;
  phonetic: string;
  chineseDefinition: string;
  chineseExample: string;
  baseForm: string;
}

const StoryDetail = () => {
  const location = useLocation();
  const { toast } = useToast();
  const { story } = location.state || {};
  
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const audioRef = useRef<HTMLAudioElement>(null);
  const [definition, setDefinition] = useState<WordDefinition | null>(null);
  const [isDefinitionOpen, setIsDefinitionOpen] = useState(false);
  const [definitionIsLoading, setDefinitionIsLoading] = useState(false);

  const generateAudioMutation = useMutation({
    mutationFn: async (text: string): Promise<string> => {
      const { data, error } = await supabase.functions.invoke('text-to-speech', {
        body: { text },
      });
      if (error) throw new Error(error.message);
      if (!data.audioContent) {
        throw new Error('AI返回的音频数据不完整，请重试。');
      }
      const audioBlob = await (await fetch(`data:audio/${data.format};base64,${data.audioContent}`)).blob();
      return URL.createObjectURL(audioBlob);
    },
    onSuccess: (url) => {
      setAudioUrl(url);
      toast({
        title: "音频生成成功",
        description: "现在可以播放故事了。",
      });
      if(audioRef.current) {
        audioRef.current.src = url;
        audioRef.current.play();
      }
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "音频生成失败",
        description: error.message,
      });
    }
  });

  const getWordDefinitionMutation = useMutation({
    mutationFn: async ({ word, context }: { word: string, context: string }): Promise<WordDefinition> => {
      setDefinitionIsLoading(true);
      const { data, error } = await supabase.functions.invoke('get-word-definition', {
        body: { word, context },
      });
      if (error) throw new Error(error.message);
      return data;
    },
    onSuccess: (data) => {
      setDefinition(data);
      setIsDefinitionOpen(true);
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "查询单词失败",
        description: error.message,
      });
    },
    onSettled: () => {
      setDefinitionIsLoading(false);
    }
  });

  const handleWordClick = (word: string, context: string) => {
    getWordDefinitionMutation.mutate({ word, context });
  };

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const onPlay = () => setIsPlaying(true);
    const onPause = () => setIsPlaying(false);
    const onEnded = () => setIsPlaying(false);

    audio.addEventListener('play', onPlay);
    audio.addEventListener('pause', onPause);
    audio.addEventListener('ended', onEnded);

    return () => {
      audio.removeEventListener('play', onPlay);
      audio.removeEventListener('pause', onPause);
      audio.removeEventListener('ended', onEnded);
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, [audioUrl]);

  const handlePlay = () => {
    if (audioUrl && audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
    } else if (story?.story) {
      generateAudioMutation.mutate(story.story);
    }
  };

  const handleDownload = () => {
    if (audioUrl) {
      const link = document.createElement('a');
      link.href = audioUrl;
      link.download = `${story.title.replace(/\s/g, '_')}.mp3`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else {
       toast({
        title: '请先生成音频',
        description: '点击 "Listen to Story" 按钮来生成音频文件。',
      });
    }
  };
  
  if (!story) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gray-100">
        <p className="text-xl text-gray-700 mb-4">故事未找到</p>
        <Link to="/stories" className="text-blue-500 hover:underline">返回故事列表</Link>
      </div>
    );
  }

  const englishParagraphs = story.story.split('\n\n');
  const chineseParagraphs = story.storyChinese ? story.storyChinese.split('\n\n') : [];

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-amber-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <Link to="/stories" className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors">
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回故事屋
          </Link>
        </div>

        <Card className="overflow-hidden rounded-2xl shadow-lg">
          <CardHeader className="p-0">
            <img src={story.imageUrl} alt={story.title} className="w-full h-64 md:h-96 object-cover" />
          </CardHeader>
          <CardContent className="p-6 md:p-10">
            <CardTitle className="text-3xl md:text-4xl font-bold mb-4">{story.title}</CardTitle>
            <div className="flex items-center space-x-4 mb-6">
              <Button onClick={handlePlay} disabled={generateAudioMutation.isPending} className="bg-gradient-to-r from-orange-500 to-amber-500 text-white">
                {generateAudioMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    生成中...
                  </>
                ) : audioUrl ? (
                  isPlaying ? (
                    <>
                      <Pause className="mr-2 h-4 w-4" />
                      Pause
                    </>
                  ) : (
                    <>
                      <Play className="mr-2 h-4 w-4" />
                      Play
                    </>
                  )
                ) : (
                  <>
                    <Play className="mr-2 h-4 w-4" />
                    Listen to Story
                  </>
                )}
              </Button>
              <Button variant="outline" onClick={handleDownload} disabled={!audioUrl}>
                <Download className="mr-2 h-4 w-4" />
                Download Audio
              </Button>
            </div>
            <div className="prose prose-lg max-w-none text-gray-700 leading-relaxed text-left">
              {englishParagraphs.map((engPara, index) => {
                const chnPara = chineseParagraphs[index];
                return (
                    <div key={index} className="mb-8">
                        <ClickableText text={engPara} onWordClick={handleWordClick} />
                        {chnPara && <p className="mt-3 text-gray-500 font-sans text-base leading-relaxed">{chnPara}</p>}
                    </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
        <audio ref={audioRef} className="hidden" />

        <Dialog open={isDefinitionOpen} onOpenChange={setIsDefinitionOpen}>
          <DialogContent className="sm:max-w-md">
            {definitionIsLoading && (
              <div className="flex items-center justify-center p-8">
                <Loader2 className="h-8 w-8 animate-spin text-orange-500" />
              </div>
            )}
            {definition && !definitionIsLoading && (
              <>
                <DialogHeader>
                  <DialogTitle className="text-3xl font-bold flex items-center">
                    {definition.word}
                    {definition.baseForm && definition.baseForm.toLowerCase() !== definition.word.toLowerCase() && (
                      <span className="text-lg text-gray-500 font-normal ml-2">({definition.baseForm})</span>
                    )}
                  </DialogTitle>
                </DialogHeader>
                <div className="mt-4 space-y-4">
                  <div className="flex items-center space-x-4">
                    {definition.phonetic && <span className="text-gray-600 font-mono text-lg">/{definition.phonetic}/</span>}
                    <Badge variant="outline">{definition.partOfSpeech}</Badge>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800 mb-1">释义 (EN)</h4>
                    <p className="text-gray-700">{definition.definition}</p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800 mb-1">释义 (CN)</h4>
                    <p className="text-gray-700">{definition.chineseDefinition}</p>
                  </div>
                   <div>
                    <h4 className="font-semibold text-gray-800 mb-1">例句 (Example)</h4>
                    <p className="text-gray-700 font-semibold">{definition.example}</p>
                    <p className="text-gray-500 mt-1">{definition.chineseExample}</p>
                  </div>
                </div>
              </>
            )}
          </DialogContent>
        </Dialog>

      </div>
    </div>
  );
};

export default StoryDetail;
