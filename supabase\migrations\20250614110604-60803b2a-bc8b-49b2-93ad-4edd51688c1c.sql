
-- 创建订阅者表来跟踪用户订阅信息
CREATE TABLE public.subscribers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT NOT NULL UNIQUE,
  stripe_customer_id TEXT,
  subscribed BOOLEAN NOT NULL DEFAULT false,
  subscription_tier TEXT,
  subscription_end TIMESTAMPTZ,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- 启用行级安全
ALTER TABLE public.subscribers ENABLE ROW LEVEL SECURITY;

-- 创建策略让用户查看自己的订阅信息
CREATE POLICY "select_own_subscription" ON public.subscribers
FOR SELECT
USING (user_id = auth.uid() OR email = auth.email());

-- 创建策略让边缘函数更新订阅信息
CREATE POLICY "update_own_subscription" ON public.subscribers
FOR UPDATE
USING (true);

-- 创建策略让边缘函数插入订阅信息
CREATE POLICY "insert_subscription" ON public.subscribers
FOR INSERT
WITH CHECK (true);

-- 创建学习进度表
CREATE TABLE public.learning_progress (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  course_type TEXT NOT NULL, -- 'chat', 'practice', 'courses'
  lesson_id TEXT,
  progress_data JSONB NOT NULL DEFAULT '{}',
  score INTEGER,
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- 启用行级安全
ALTER TABLE public.learning_progress ENABLE ROW LEVEL SECURITY;

-- 创建策略让用户管理自己的学习进度
CREATE POLICY "manage_own_progress" ON public.learning_progress
FOR ALL
USING (user_id = auth.uid());
