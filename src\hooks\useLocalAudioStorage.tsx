import { useCallback } from 'react';

// IndexedDB 数据库配置
const DB_NAME = 'SpeakSmartAudioDB';
const DB_VERSION = 1;
const STORE_NAME = 'audioFiles';

interface AudioData {
  id: string;
  blob: Blob;
  url: string;
  timestamp: number;
  title: string;
}

// 初始化 IndexedDB
const initDB = (): Promise<IDBDatabase> => {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open(DB_NAME, DB_VERSION);
    
    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve(request.result);
    
    request.onupgradeneeded = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;
      if (!db.objectStoreNames.contains(STORE_NAME)) {
        const store = db.createObjectStore(STORE_NAME, { keyPath: 'id' });
        store.createIndex('timestamp', 'timestamp', { unique: false });
      }
    };
  });
};

export const useLocalAudioStorage = () => {
  
  // 保存音频到本地
  const saveAudioUrl = useCallback(async (storyId: string, audioBlob: Blob, title?: string): Promise<void> => {
    try {
      const db = await initDB();
      const transaction = db.transaction([STORE_NAME], 'readwrite');
      const store = transaction.objectStore(STORE_NAME);
      
      // 创建 URL
      const url = URL.createObjectURL(audioBlob);
      
      const audioData: AudioData = {
        id: storyId,
        blob: audioBlob,
        url,
        timestamp: Date.now(),
        title: title || `Story ${storyId}`
      };
      
      await new Promise<void>((resolve, reject) => {
        const request = store.put(audioData);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });
      
      console.log(`Audio saved for story ${storyId}`);
    } catch (error) {
      console.error('Error saving audio:', error);
      throw error;
    }
  }, []);

  // 获取本地音频URL
  const getAudioUrl = useCallback(async (storyId: string): Promise<string | null> => {
    try {
      const db = await initDB();
      const transaction = db.transaction([STORE_NAME], 'readonly');
      const store = transaction.objectStore(STORE_NAME);
      
      const audioData = await new Promise<AudioData | null>((resolve, reject) => {
        const request = store.get(storyId);
        request.onsuccess = () => resolve(request.result || null);
        request.onerror = () => reject(request.error);
      });
      
      if (audioData) {
        // 检查 URL 是否仍然有效，如果无效则重新创建
        try {
          const response = await fetch(audioData.url);
          if (response.ok) {
            return audioData.url;
          }
        } catch {
          // URL 无效，重新创建
          const newUrl = URL.createObjectURL(audioData.blob);
          // 更新数据库中的 URL
          await updateAudioUrl(storyId, newUrl);
          return newUrl;
        }
      }
      
      return null;
    } catch (error) {
      console.error('Error getting audio:', error);
      return null;
    }
  }, []);

  // 更新音频URL
  const updateAudioUrl = useCallback(async (storyId: string, newUrl: string): Promise<void> => {
    try {
      const db = await initDB();
      const transaction = db.transaction([STORE_NAME], 'readwrite');
      const store = transaction.objectStore(STORE_NAME);
      
      const audioData = await new Promise<AudioData | null>((resolve, reject) => {
        const request = store.get(storyId);
        request.onsuccess = () => resolve(request.result || null);
        request.onerror = () => reject(request.error);
      });
      
      if (audioData) {
        audioData.url = newUrl;
        audioData.timestamp = Date.now();
        
        await new Promise<void>((resolve, reject) => {
          const request = store.put(audioData);
          request.onsuccess = () => resolve();
          request.onerror = () => reject(request.error);
        });
      }
    } catch (error) {
      console.error('Error updating audio URL:', error);
    }
  }, []);

  // 删除本地音频
  const deleteAudioUrl = useCallback(async (storyId: string): Promise<void> => {
    try {
      const db = await initDB();
      const transaction = db.transaction([STORE_NAME], 'readwrite');
      const store = transaction.objectStore(STORE_NAME);
      
      // 先获取数据以释放 URL
      const audioData = await new Promise<AudioData | null>((resolve, reject) => {
        const request = store.get(storyId);
        request.onsuccess = () => resolve(request.result || null);
        request.onerror = () => reject(request.error);
      });
      
      if (audioData) {
        URL.revokeObjectURL(audioData.url);
      }
      
      await new Promise<void>((resolve, reject) => {
        const request = store.delete(storyId);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });
      
      console.log(`Audio deleted for story ${storyId}`);
    } catch (error) {
      console.error('Error deleting audio:', error);
      throw error;
    }
  }, []);

  // 获取所有本地音频
  const getAllAudioData = useCallback(async (): Promise<AudioData[]> => {
    try {
      const db = await initDB();
      const transaction = db.transaction([STORE_NAME], 'readonly');
      const store = transaction.objectStore(STORE_NAME);
      
      return new Promise<AudioData[]>((resolve, reject) => {
        const request = store.getAll();
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.error('Error getting all audio data:', error);
      return [];
    }
  }, []);

  // 清理过期的音频文件（超过30天）
  const cleanupOldAudio = useCallback(async (): Promise<void> => {
    try {
      const db = await initDB();
      const transaction = db.transaction([STORE_NAME], 'readwrite');
      const store = transaction.objectStore(STORE_NAME);
      const index = store.index('timestamp');
      
      const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
      const range = IDBKeyRange.upperBound(thirtyDaysAgo);
      
      const request = index.openCursor(range);
      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result;
        if (cursor) {
          const audioData = cursor.value as AudioData;
          URL.revokeObjectURL(audioData.url);
          cursor.delete();
          cursor.continue();
        }
      };
    } catch (error) {
      console.error('Error cleaning up old audio:', error);
    }
  }, []);

  // 获取存储使用情况
  const getStorageInfo = useCallback(async (): Promise<{ count: number; totalSize: number }> => {
    try {
      const allData = await getAllAudioData();
      let totalSize = 0;
      
      for (const data of allData) {
        totalSize += data.blob.size;
      }
      
      return {
        count: allData.length,
        totalSize
      };
    } catch (error) {
      console.error('Error getting storage info:', error);
      return { count: 0, totalSize: 0 };
    }
  }, [getAllAudioData]);

  return {
    saveAudioUrl,
    getAudioUrl,
    deleteAudioUrl,
    getAllAudioData,
    cleanupOldAudio,
    getStorageInfo
  };
};
