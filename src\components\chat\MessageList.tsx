
import React, { useRef, useEffect } from 'react';
import { Bo<PERSON>, User, PlayCircle, PauseCircle } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import ClickableText from '@/components/ClickableText';
import type { ChatMessage } from '@/hooks/useAIChat';

interface MessageListProps {
  messages: ChatMessage[];
  loading: boolean;
  activeSpeech: { timestamp: Date; isPaused: boolean } | null;
  onWordClick: (word: string, context: string) => void;
  toggleMessagePlayback: (message: ChatMessage) => void;
}

const MessageList = ({
  messages,
  loading,
  activeSpeech,
  onWordClick,
  toggleMessagePlayback,
}: MessageListProps) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  return (
    <ScrollArea className="flex-1 p-4">
      <div className="space-y-4">
        {messages.length === 0 && (
          <div className="text-center text-gray-500 py-8">
            <Bot className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>你好！我是你的英语学习助手。</p>
            <p>有什么英语问题需要我帮助吗？</p>
          </div>
        )}

        {messages.map((message, index) => {
          const isPlaying = activeSpeech?.timestamp.getTime() === message.timestamp.getTime();
          const isPaused = isPlaying && activeSpeech?.isPaused;

          return (
            <div
              key={index}
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`flex items-start space-x-2 max-w-[80%] ${
                  message.role === 'user' ? 'flex-row-reverse space-x-reverse' : ''
                }`}
              >
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center shrink-0 ${
                    message.role === 'user'
                      ? 'bg-blue-600 text-white'
                      : 'bg-emerald-600 text-white'
                  }`}
                >
                  {message.role === 'user' ? (
                    <User className="w-4 h-4" />
                  ) : (
                    <Bot className="w-4 h-4" />
                  )}
                </div>
                <div
                  className={`p-3 rounded-lg ${
                    message.role === 'user'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-900'
                  }`}
                >
                  {message.role === 'assistant' ? (
                    <ClickableText text={message.content} onWordClick={onWordClick} />
                  ) : (
                    <p className="whitespace-pre-wrap">{message.content}</p>
                  )}
                  <div className="flex justify-between items-center mt-2">
                    <p className="text-xs opacity-70">
                      {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </p>
                    <div className="flex items-center space-x-1">
                      {message.model && message.role === 'assistant' && (
                        <p className="text-xs opacity-70">{message.model}</p>
                      )}
                      {message.role === 'assistant' && (
                        <Button
                          size="icon"
                          variant="ghost"
                          onClick={() => toggleMessagePlayback(message)}
                          className="w-6 h-6 text-gray-500 hover:text-gray-900"
                        >
                          {isPlaying && !isPaused ? (
                            <PauseCircle className="w-4 h-4" />
                          ) : (
                            <PlayCircle className="w-4 h-4" />
                          )}
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          );
        })}

        {loading && (
          <div className="flex justify-start">
            <div className="flex items-start space-x-2">
              <div className="w-8 h-8 rounded-full bg-emerald-600 text-white flex items-center justify-center">
                <Bot className="w-4 h-4" />
              </div>
              <div className="bg-gray-100 p-3 rounded-lg">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
      <div ref={messagesEndRef} />
    </ScrollArea>
  );
};

export default MessageList;
