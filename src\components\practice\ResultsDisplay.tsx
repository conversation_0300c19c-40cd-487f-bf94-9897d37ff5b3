
import React from 'react';

interface Question {
  question: string;
  options: string[];
  correct: number;
}

interface ResultsDisplayProps {
  questions: Question[];
  answers: { [key: number]: number };
  showResults: boolean;
  currentExercise: number;
  totalExercises: number;
  onCheckAnswers: () => void;
  onRetry: () => void;
  onNextExercise: () => void;
}

const ResultsDisplay = ({
  questions,
  answers,
  showResults,
  currentExercise,
  totalExercises,
  onCheckAnswers,
  onRetry,
  onNextExercise
}: ResultsDisplayProps) => {
  const getScore = () => {
    let correct = 0;
    questions.forEach((question, index) => {
      if (answers[index] === question.correct) {
        correct++;
      }
    });
    return correct;
  };

  return (
    <div className="mt-8 text-center">
      {!showResults ? (
        <button
          onClick={onCheckAnswers}
          disabled={Object.keys(answers).length !== questions.length}
          className="px-8 py-3 bg-gradient-to-r from-blue-500 to-emerald-500 text-white rounded-xl font-semibold hover:shadow-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          检查答案
        </button>
      ) : (
        <div className="space-y-4">
          <div className="text-xl font-semibold">
            得分: {getScore()}/{questions.length}
          </div>
          <div className="flex justify-center space-x-4">
            <button
              onClick={onRetry}
              className="px-6 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
            >
              重新练习
            </button>
            {currentExercise < totalExercises - 1 && (
              <button
                onClick={onNextExercise}
                className="px-6 py-2 bg-gradient-to-r from-blue-500 to-emerald-500 text-white rounded-lg hover:shadow-lg transition-all duration-300"
              >
                下一题
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ResultsDisplay;
