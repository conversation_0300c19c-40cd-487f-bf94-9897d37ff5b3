
import { Exercise } from '@/hooks/usePracticeState';

export const exercises: Exercise[] = [
  {
    id: '1',
    title: '日常对话：在咖啡店',
    audioUrl: '/audio/coffee-shop.mp3',
    transcript: `A: Good morning! What can I get for you today?
B: I'd like a large coffee with milk, please.
A: Would you like anything else?
B: Yes, I'll have a blueberry muffin too.
A: That'll be $8.50. Here or to go?
B: To go, please. Thank you!`,
    questions: [
      {
        question: 'What did the customer order to drink?',
        options: ['Tea with sugar', 'Large coffee with milk', 'Small black coffee', 'Hot chocolate'],
        correct: 1
      },
      {
        question: 'What food item did the customer order?',
        options: ['Chocolate cake', 'Apple pie', 'Blueberry muffin', 'Croissant'],
        correct: 2
      },
      {
        question: 'How much was the total cost?',
        options: ['$7.50', '$8.50', '$9.50', '$6.50'],
        correct: 1
      }
    ],
    difficulty: 'beginner'
  },
  {
    id: '2',
    title: '商务会议：项目讨论',
    audioUrl: '/audio/business-meeting.mp3',
    transcript: `A: Let's review the quarterly sales report. How are we performing?
B: We're up 15% compared to last quarter, which exceeds our target.
A: That's excellent news. What about the new product launch?
B: The launch is scheduled for next month. Marketing has prepared the campaign.
A: Great. Let's make sure we're ready for the increased demand.`,
    questions: [
      {
        question: 'How much did sales increase compared to last quarter?',
        options: ['10%', '15%', '20%', '25%'],
        correct: 1
      },
      {
        question: 'When is the new product launch scheduled?',
        options: ['This month', 'Next month', 'Next quarter', 'Next year'],
        correct: 1
      }
    ],
    difficulty: 'intermediate'
  },
  {
    id: '3',
    title: '旅游场景：机场问讯',
    audioUrl: '/audio/airport.mp3',
    transcript: `A: Excuse me, where can I check in for flight 305?
B: The check-in counters for flight 305 are at aisle C.
A: Thank you. Is there a lounge nearby?
B: Yes, it's next to gate 12 after security.
A: Great, thanks for your help!`,
    questions: [
      {
        question: 'Where are the check-in counters for flight 305?',
        options: ['Aisle A', 'Aisle B', 'Aisle C', 'Aisle D'],
        correct: 2
      },
      {
        question: 'Where is the lounge located?',
        options: ['Before security', 'Next to gate 12', 'By the check-in area', 'Beside duty free'],
        correct: 1
      }
    ],
    difficulty: 'beginner'
  },
  {
    id: '4',
    title: '学术讨论会：技术创新',
    audioUrl: '/audio/tech-innovation.mp3',
    transcript: `A: This conference focuses on the latest innovations in AI.
B: Will there be a session on robotics?
A: Yes, tomorrow morning at 9am.
B: I see, and will we get access to the presentation slides?
A: Absolutely, they will be emailed after the session.`,
    questions: [
      {
        question: 'What is the main topic of the conference?',
        options: ['Business trends', 'AI innovations', 'Marketing', 'Project management'],
        correct: 1
      },
      {
        question: 'When is the robotics session?',
        options: ['Today morning', 'Tomorrow morning', 'Tomorrow afternoon', 'Next week'],
        correct: 1
      },
      {
        question: 'How will attendees receive the slides?',
        options: ['Via email', 'Printouts', 'QR code on-screen', 'Not available'],
        correct: 0
      }
    ],
    difficulty: 'intermediate'
  }
];
