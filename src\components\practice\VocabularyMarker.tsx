
import React, { useState, useEffect } from 'react';
import { BookOpen, Star, Trash2, Download } from 'lucide-react';

interface VocabularyMarkerProps {
  transcript: string;
  onVocabularyUpdate: (vocabulary: string[]) => void;
}

interface MarkedWord {
  word: string;
  definition?: string;
  marked: boolean;
}

const VocabularyMarker = ({ transcript, onVocabularyUpdate }: VocabularyMarkerProps) => {
  const [markedWords, setMarkedWords] = useState<MarkedWord[]>([]);
  const [selectedWord, setSelectedWord] = useState<string | null>(null);
  const [definition, setDefinition] = useState('');
  const [showVocabularyList, setShowVocabularyList] = useState(false);

  useEffect(() => {
    // 将文本分词并初始化标记状态
    const words = transcript.split(/\s+/).map(word => ({
      word: word.replace(/[.,!?;:]$/, ''), // 移除标点
      marked: false,
      definition: ''
    }));
    setMarkedWords(words);
  }, [transcript]);

  const handleWordClick = (word: string, index: number) => {
    setSelectedWord(word);
    setDefinition(markedWords[index].definition || '');
  };

  const handleMarkWord = () => {
    if (selectedWord) {
      const newMarkedWords = markedWords.map(item => 
        item.word === selectedWord 
          ? { ...item, marked: true, definition: definition }
          : item
      );
      setMarkedWords(newMarkedWords);
      
      // 更新词汇本
      const vocabulary = newMarkedWords
        .filter(item => item.marked)
        .map(item => `${item.word}: ${item.definition || '(无释义)'}`);
      onVocabularyUpdate(vocabulary);
      
      setSelectedWord(null);
      setDefinition('');
    }
  };

  const handleUnmarkWord = (word: string) => {
    const newMarkedWords = markedWords.map(item => 
      item.word === word ? { ...item, marked: false, definition: '' } : item
    );
    setMarkedWords(newMarkedWords);
    
    const vocabulary = newMarkedWords
      .filter(item => item.marked)
      .map(item => `${item.word}: ${item.definition || '(无释义)'}`);
    onVocabularyUpdate(vocabulary);
  };

  const exportVocabulary = () => {
    const vocabulary = markedWords
      .filter(item => item.marked)
      .map(item => `${item.word}: ${item.definition || '(无释义)'}`)
      .join('\n');
    
    const blob = new Blob([vocabulary], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'vocabulary.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getMarkedVocabulary = () => {
    return markedWords.filter(item => item.marked);
  };

  return (
    <div className="bg-white rounded-2xl shadow-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-xl font-semibold text-gray-900 flex items-center">
          <BookOpen size={24} className="mr-2" />
          词汇标记
        </h3>
        <div className="flex space-x-2">
          <button
            onClick={() => setShowVocabularyList(!showVocabularyList)}
            className="px-3 py-1 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors text-sm"
          >
            词汇本 ({getMarkedVocabulary().length})
          </button>
          {getMarkedVocabulary().length > 0 && (
            <button
              onClick={exportVocabulary}
              className="px-3 py-1 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors text-sm flex items-center space-x-1"
            >
              <Download size={16} />
              <span>导出</span>
            </button>
          )}
        </div>
      </div>

      <div className="space-y-4">
        {/* 文本区域 */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-semibold text-gray-900 mb-2">点击词汇进行标记：</h4>
          <div className="text-gray-800 leading-relaxed">
            {markedWords.map((wordObj, index) => (
              <span key={index}>
                <button
                  onClick={() => handleWordClick(wordObj.word, index)}
                  className={`px-1 py-0.5 rounded transition-colors ${
                    wordObj.marked
                      ? 'bg-yellow-200 text-yellow-800 hover:bg-yellow-300'
                      : 'hover:bg-gray-200'
                  } ${
                    selectedWord === wordObj.word ? 'ring-2 ring-blue-500' : ''
                  }`}
                >
                  {wordObj.word}
                </button>
                <span> </span>
              </span>
            ))}
          </div>
        </div>

        {/* 词汇标记面板 */}
        {selectedWord && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-semibold text-blue-900 mb-2">
              标记词汇: "{selectedWord}"
            </h4>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-blue-700 mb-1">
                  添加释义或笔记:
                </label>
                <input
                  type="text"
                  value={definition}
                  onChange={(e) => setDefinition(e.target.value)}
                  className="w-full p-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="输入词汇的含义或笔记..."
                />
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={handleMarkWord}
                  className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-1"
                >
                  <Star size={16} />
                  <span>标记</span>
                </button>
                <button
                  onClick={() => {
                    setSelectedWord(null);
                    setDefinition('');
                  }}
                  className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                >
                  取消
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 词汇本列表 */}
        {showVocabularyList && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h4 className="font-semibold text-green-900 mb-3">我的词汇本</h4>
            {getMarkedVocabulary().length === 0 ? (
              <p className="text-green-700 text-sm">还没有标记任何词汇</p>
            ) : (
              <div className="space-y-2">
                {getMarkedVocabulary().map((wordObj, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between bg-white rounded-lg p-3"
                  >
                    <div>
                      <span className="font-medium text-gray-900">{wordObj.word}</span>
                      {wordObj.definition && (
                        <span className="text-gray-600 ml-2">- {wordObj.definition}</span>
                      )}
                    </div>
                    <button
                      onClick={() => handleUnmarkWord(wordObj.word)}
                      className="text-red-500 hover:text-red-700 transition-colors"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default VocabularyMarker;
