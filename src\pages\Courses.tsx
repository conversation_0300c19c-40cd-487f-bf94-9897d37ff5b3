import React from 'react';
import { Book<PERSON><PERSON>, Clock, Users, Star, Play } from 'lucide-react';
import { useNavigate } from "react-router-dom";

const Courses = () => {
  const navigate = useNavigate();

  const courses = [
    {
      id: 1,
      title: '英语基础入门',
      description: '从零开始学习英语，掌握基本语法和常用词汇',
      level: '初级',
      duration: '4周',
      students: 1200,
      rating: 4.8,
      image: 'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
      color: 'from-green-500 to-emerald-600'
    },
    {
      id: 2,
      title: '商务英语进阶',
      description: '职场英语必备，提升商务沟通和写作能力',
      level: '中级',
      duration: '6周',
      students: 800,
      rating: 4.9,
      image: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
      color: 'from-blue-500 to-blue-600'
    },
    {
      id: 3,
      title: '雅思托福冲刺',
      description: '针对性备考训练，快速提升考试成绩',
      level: '高级',
      duration: '8周',
      students: 600,
      rating: 4.7,
      image: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
      color: 'from-purple-500 to-purple-600'
    },
    {
      id: 4,
      title: '日常英语对话',
      description: '生活场景英语练习，提升口语流利度',
      level: '中级',
      duration: '5周',
      students: 950,
      rating: 4.6,
      image: 'https://images.unsplash.com/photo-1573164713714-d95e436ab8d6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
      color: 'from-orange-500 to-red-500'
    },
    {
      id: 5,
      title: '英语语法精讲',
      description: '系统学习英语语法规则，打牢语言基础',
      level: '初级-中级',
      duration: '6周',
      students: 1100,
      rating: 4.8,
      image: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
      color: 'from-teal-500 to-cyan-600'
    },
    {
      id: 6,
      title: '英语听力提升',
      description: '多场景听力训练，快速提高理解能力',
      level: '中级-高级',
      duration: '4周',
      students: 700,
      rating: 4.9,
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
      color: 'from-indigo-500 to-purple-600'
    },
    {
      id: 7,
      title: '口语表达实战',
      description: '通过真实情景模拟和对话练习，让口语自然流利',
      level: '初级-高级',
      duration: '5周',
      students: 850,
      rating: 4.9,
      image: 'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      color: 'from-pink-500 to-rose-600'
    },
    {
      id: 8,
      title: '英美文化体验课',
      description: '通过影视与文学了解英语国家文化，提高语言感知',
      level: '中级',
      duration: '4周',
      students: 600,
      rating: 4.7,
      image: 'https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      color: 'from-yellow-500 to-orange-400'
    },
    {
      id: 9,
      title: '英语写作与改进',
      description: '实用写作训练，提升写作结构和表达，适合留学及职场需求',
      level: '中级-高级',
      duration: '7周',
      students: 720,
      rating: 4.85,
      image: 'https://images.unsplash.com/photo-1487058792275-0ad4aaf24ca7?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      color: 'from-lime-500 to-green-400'
    },
  ];

  const getLevelColor = (level: string) => {
    if (level.includes('初级')) return 'bg-green-100 text-green-800';
    if (level.includes('中级')) return 'bg-blue-100 text-blue-800';
    if (level.includes('高级')) return 'bg-purple-100 text-purple-800';
    return 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            精品课程
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            系统化的英语学习路径，从基础到高级，满足不同学习需求
          </p>
        </div>

        {/* Course Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {courses.map((course) => (
            <div
              key={course.id}
              className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 group cursor-pointer"
              onClick={() => navigate(`/courses/${course.id}`)}
            >
              <div className="relative">
                <img
                  src={course.image}
                  alt={course.title}
                  className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className={`absolute top-4 left-4 px-3 py-1 rounded-full text-sm font-semibold text-white bg-gradient-to-r ${course.color}`}>
                  {course.level}
                </div>
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                  <button className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-white text-gray-900 rounded-full p-3 hover:scale-110 transform pointer-events-none">
                    <Play size={24} />
                  </button>
                </div>
              </div>

              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  {course.title}
                </h3>
                <p className="text-gray-600 mb-4 line-clamp-2">
                  {course.description}
                </p>

                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <div className="flex items-center">
                    <Clock size={16} className="mr-1" />
                    {course.duration}
                  </div>
                  <div className="flex items-center">
                    <Users size={16} className="mr-1" />
                    {course.students}
                  </div>
                  <div className="flex items-center">
                    <Star size={16} className="mr-1 text-yellow-500" />
                    {course.rating}
                  </div>
                </div>

                <button className={`w-full py-3 rounded-xl font-semibold text-white bg-gradient-to-r ${course.color} hover:shadow-lg transition-all duration-300 transform hover:scale-105 pointer-events-none`}>
                  开始学习
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <div className="mt-16 text-center bg-gradient-to-r from-blue-600 to-emerald-600 rounded-2xl p-12 text-white">
          <h2 className="text-3xl font-bold mb-4">
            还没找到适合的课程？
          </h2>
          <p className="text-xl mb-8 opacity-90">
            联系我们的学习顾问，获得个性化的学习建议
          </p>
          <button className="bg-white text-blue-600 px-8 py-4 rounded-xl font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105">
            咨询学习顾问
          </button>
        </div>
      </div>
    </div>
  );
};

export default Courses;
