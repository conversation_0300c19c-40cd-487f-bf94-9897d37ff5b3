
import 'https://deno.land/x/xhr@0.1.0/mod.ts'
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// Helper function to decode base64
function processBase64(base64String: string): Uint8Array {
  try {
    const binaryString = atob(base64String);
    const len = binaryString.length;
    const bytes = new Uint8Array(len);
    for (let i = 0; i < len; i++) {
        bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes;
  } catch (e) {
    console.error("Failed to decode base64 string:", e);
    throw new Error("Invalid base64 string provided.");
  }
}

serve(async (req) => {
  console.log(`Speech evaluation request received: ${req.method} ${req.url}`);
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('Speech evaluation function started.');
    const { audio, originalTranscript } = await req.json()

    if (!audio || !originalTranscript) {
      console.error('Missing audio data or original transcript.');
      throw new Error('Audio data and original transcript are required.')
    }
    console.log('Received audio and transcript.');

    const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
    if (!openAIApiKey) {
      console.error('OPENAI_API_KEY is not set.');
      throw new Error("OPENAI_API_KEY is not set in environment variables.");
    }
    console.log('Found OpenAI API key.');

    // 1. Transcribe audio with Whisper
    const binaryAudio = processBase64(audio)
    const formData = new FormData()
    const blob = new Blob([binaryAudio], { type: 'audio/webm' })
    formData.append('file', blob, 'audio.webm')
    formData.append('model', 'whisper-1')
    formData.append('language', 'en');

    console.log('Sending audio to OpenAI for transcription...');
    const transcriptionResponse = await fetch('https://api.openai.com/v1/audio/transcriptions', {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${openAIApiKey}` },
      body: formData,
    })
    console.log(`Transcription API response status: ${transcriptionResponse.status}`);

    if (!transcriptionResponse.ok) {
      const errorText = await transcriptionResponse.text();
      console.error('OpenAI Transcription API error:', errorText);
      throw new Error(`Transcription failed: ${errorText}`)
    }

    const transcriptionResult = await transcriptionResponse.json()
    const userTranscript = transcriptionResult.text
    console.log('Transcription successful:', userTranscript);

    // 2. Evaluate transcription with GPT
    const prompt = `You are an English pronunciation and fluency evaluator.
Your task is to compare the original text with the user's transcribed speech.
The user's transcription might have errors from the speech-to-text model, so be tolerant of minor discrepancies.
Focus on overall accuracy and completeness.
Provide a score from 0 to 100 based on how closely the user's speech matches the original text.
Also provide brief, constructive feedback in Chinese, pointing out any major omissions or differences.
You MUST respond with a single, valid JSON object with two keys: "score" (a number) and "feedback" (a string in Chinese).
Do not include any text outside of the JSON object.

Original Text:
---
${originalTranscript}
---

User's Transcribed Text:
---
${userTranscript}
---

Your JSON response:`

    console.log('Sending transcription to OpenAI for evaluation...');
    console.log("Evaluation prompt being sent to OpenAI:", prompt);
    const evaluationResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [{ role: 'user', content: prompt }],
        response_format: { type: "json_object" },
        temperature: 0.2,
      }),
    });
    console.log(`Evaluation API response status: ${evaluationResponse.status}`);

    const responseBodyText = await evaluationResponse.text();
    console.log(`Raw evaluation response body from OpenAI: ${responseBodyText}`);

    if (!evaluationResponse.ok) {
      console.error('OpenAI Chat API error response:', responseBodyText);
      throw new Error(`Evaluation failed with status ${evaluationResponse.status}: ${responseBodyText}`);
    }
    
    const evaluationResult = JSON.parse(responseBodyText);
    console.log('Raw evaluation result from OpenAI (parsed):', JSON.stringify(evaluationResult));

    const choice = evaluationResult.choices?.[0];
    if (!choice || !choice.message || !choice.message.content) {
      console.error('Invalid response structure from OpenAI Chat API:', JSON.stringify(evaluationResult));
      throw new Error('AI evaluation returned an invalid response format.');
    }

    let content = choice.message.content;
    // Sometimes the AI wraps the JSON in markdown, so we try to extract it.
    const jsonMatch = content.match(/```(?:json)?\n([\s\S]*?)\n```/);
    if (jsonMatch && jsonMatch[1]) {
        console.log('Extracted JSON from markdown block.');
        content = jsonMatch[1];
    }

    let resultData;
    try {
      resultData = JSON.parse(content);
    } catch (parseError) {
      console.error('Failed to parse JSON from AI response:', content, parseError);
      throw new Error('AI evaluation returned a non-JSON response.');
    }
    
    console.log('Evaluation successful, parsed data:', resultData);

    // Add user's transcript to the final response
    resultData.userTranscript = userTranscript;

    return new Response(JSON.stringify(resultData), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })

  } catch (error) {
    console.error('Error in speech-evaluation function:', error.message);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  }
})
