# 本地化 AI 故事屋 - 设置指南

## 🎉 完全本地化解决方案

我已经为您创建了一个完全本地化的 AI 故事屋，不再依赖 Supabase 后端！现在您可以：

- ✅ **使用 MiniMax API** 进行故事生成和音频合成
- ✅ **完全本地存储** 所有数据
- ✅ **无需后端服务器** 
- ✅ **支持多种 AI 提供商**
- ✅ **浏览器原生 TTS 备选方案**

## 🚀 快速开始

### 1. 启动应用

```bash
npm run dev
```

### 2. 配置 API 密钥

1. 打开应用后，点击右上角的 **"API 配置"** 按钮
2. 配置您的 MiniMax API 信息：
   - **API Key**: 您的 MiniMax API 密钥
   - **Group ID**: 您的 MiniMax Group ID
   - **模型**: 选择 `abab6.5s-chat` 或其他模型

### 3. 开始使用

1. 在故事创建框中输入您的想法
2. 点击 **"开始创作"** 生成故事
3. 点击故事卡片的 **"Listen"** 按钮生成和播放音频
4. 所有内容自动保存到本地

## 🔧 支持的 AI 提供商

### MiniMax (推荐)
- **故事生成**: ✅ 支持
- **音频合成**: ✅ 支持
- **配置**: API Key + Group ID

### SiliconFlow
- **故事生成**: ✅ 支持
- **音频合成**: ❌ 需要配合浏览器 TTS
- **配置**: API Key

### OpenAI
- **故事生成**: ✅ 支持
- **音频合成**: ❌ 需要配合浏览器 TTS
- **配置**: API Key

### Ollama (本地)
- **故事生成**: ✅ 支持
- **音频合成**: ❌ 需要配合浏览器 TTS
- **配置**: Base URL (默认: http://localhost:11434)

## 📱 功能特性

### 🎨 故事生成
- 根据提示生成英文故事
- 自动提供中文翻译
- 智能标题和描述生成
- 本地存储，永久保存

### 🎵 音频合成
- **MiniMax TTS**: 高质量语音合成
- **浏览器 TTS**: 备选方案，无需 API
- 多种语音选择
- 语速、音量、音调调节

### 💾 本地存储
- **IndexedDB**: 存储音频文件
- **LocalStorage**: 存储故事内容
- **自动清理**: 30天未访问的内容
- **数据导出/导入**: 跨设备同步

### ⚙️ 配置管理
- **API 密钥管理**: 安全的本地存储
- **连接测试**: 验证 API 配置
- **多提供商支持**: 灵活切换
- **参数调节**: 自定义语音参数

## 🛠️ 技术架构

### 新增文件
```
src/
├── services/
│   ├── localAIService.ts      # 本地 AI 服务
│   └── localAudioService.ts   # 本地音频服务
├── hooks/
│   └── useLocalAI.tsx         # 本地 AI hooks
└── components/
    └── ConfigManager.tsx      # 配置管理组件
```

### 核心服务

#### LocalAIService
- 支持多种 AI 提供商
- 统一的 API 接口
- 自动错误处理和重试

#### LocalAudioService
- MiniMax TTS 集成
- 浏览器原生 TTS 备选
- 音频参数控制

#### 本地存储系统
- 双重存储策略
- 自动数据管理
- 跨会话持久化

## 🔐 隐私和安全

### 数据安全
- ✅ **API 密钥本地存储**: 不会上传到服务器
- ✅ **故事内容本地保存**: 完全私有
- ✅ **音频文件本地缓存**: 无需重复生成
- ✅ **无服务器依赖**: 减少数据泄露风险

### 配置安全
- API 密钥使用密码字段隐藏
- 本地存储加密（浏览器级别）
- 可随时清除所有数据

## 📋 使用步骤

### 首次设置
1. **获取 MiniMax API 密钥**
   - 访问 [MiniMax 官网](https://api.minimaxi.com/)
   - 注册账号并获取 API Key 和 Group ID

2. **配置应用**
   - 点击 "API 配置" 按钮
   - 输入您的 API 信息
   - 点击 "测试连接" 验证配置
   - 点击 "保存配置"

### 日常使用
1. **创建故事**
   - 输入故事提示
   - 点击 "开始创作"
   - 等待 AI 生成故事

2. **播放音频**
   - 点击故事卡片的 "Listen" 按钮
   - 首次会生成音频（需要等待）
   - 后续访问直接播放缓存音频

3. **管理数据**
   - 点击 "存储管理" 查看使用情况
   - 定期清理过期数据
   - 导出重要故事数据

## 🚨 故障排除

### 常见问题

#### 1. 故事生成失败
- **检查 API 配置**: 确保 API Key 和 Group ID 正确
- **测试连接**: 使用配置页面的测试功能
- **检查网络**: 确保能访问 MiniMax API

#### 2. 音频生成失败
- **MiniMax 配置**: 检查音频服务的 API 配置
- **浏览器 TTS**: 如果 MiniMax 失败，会自动使用浏览器 TTS
- **浏览器支持**: 确保浏览器支持 Web Speech API

#### 3. 数据丢失
- **浏览器存储**: 检查浏览器是否清除了本地数据
- **导出备份**: 定期导出重要数据
- **存储限制**: 检查浏览器存储空间

### 错误代码

| 错误信息 | 原因 | 解决方案 |
|---------|------|---------|
| "AI 服务未配置" | 未设置 API 密钥 | 在配置页面设置 API 信息 |
| "MiniMax API 错误" | API 调用失败 | 检查 API 密钥和网络连接 |
| "浏览器不支持语音合成" | 浏览器兼容性问题 | 使用现代浏览器或配置 MiniMax |

## 🔄 数据迁移

### 从 Supabase 迁移
如果您之前使用过 Supabase 版本：

1. **导出旧数据** (如果可能)
2. **清理配置**: 移除 Supabase 相关配置
3. **重新配置**: 设置新的 API 密钥
4. **重新生成**: 故事和音频会重新生成并保存到本地

### 跨设备同步
1. **导出数据**: 在源设备上导出故事数据
2. **传输文件**: 将 JSON 文件传输到目标设备
3. **导入数据**: 在目标设备上导入数据
4. **重新配置**: 在新设备上配置 API 密钥

## 🎯 下一步优化

### 计划中的功能
- [ ] **离线模式**: 完全离线的故事生成
- [ ] **语音识别**: 语音输入故事提示
- [ ] **多语言支持**: 支持更多语言
- [ ] **故事分类**: 按主题分类管理
- [ ] **社区分享**: 本地故事分享功能

### 性能优化
- [ ] **音频压缩**: 减少存储空间占用
- [ ] **懒加载**: 按需加载故事内容
- [ ] **缓存策略**: 智能缓存管理
- [ ] **批量操作**: 批量生成和管理

---

现在您拥有了一个完全本地化、功能完整的 AI 故事屋！🎉

所有数据都保存在您的设备上，隐私安全，使用便捷。开始创作您的专属英语故事吧！
