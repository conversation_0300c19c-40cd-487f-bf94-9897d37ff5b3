
import { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/components/ui/use-toast';

export const useAudioStorage = () => {
  const { toast } = useToast();
  
  // 上传音频文件到Supabase Storage
  const uploadAudioMutation = useMutation({
    mutationFn: async ({ file, storyId }: { file: File; storyId: string }) => {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) throw new Error('用户未登录');

      const fileName = `${user.user.id}/${storyId}/audio.mp3`;
      
      const { data, error } = await supabase.storage
        .from('story-audio')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: true
        });

      if (error) throw error;

      // 获取公开URL
      const { data: urlData } = supabase.storage
        .from('story-audio')
        .getPublicUrl(fileName);

      return urlData.publicUrl;
    },
    onSuccess: (audioUrl) => {
      toast({
        title: "音频上传成功！",
        description: "音频文件已保存，下次访问时可直接播放。",
      });
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "音频上传失败",
        description: error.message,
      });
    }
  });

  // 生成音频并上传
  const generateAndUploadAudioMutation = useMutation({
    mutationFn: async ({ text, storyId }: { text: string; storyId: string }) => {
      // 调用音频生成API
      const { data: audioData, error: audioError } = await supabase.functions.invoke('text-to-speech', {
        body: { text },
      });
      
      if (audioError) throw new Error(audioError.message);
      if (!audioData?.audioData) throw new Error('音频生成失败');

      // 将base64音频数据转换为Blob
      const audioBlob = new Blob([
        Uint8Array.from(atob(audioData.audioData), c => c.charCodeAt(0))
      ], { type: 'audio/mpeg' });

      // 创建File对象
      const audioFile = new File([audioBlob], 'generated-audio.mp3', { type: 'audio/mpeg' });

      // 上传到存储
      return uploadAudioMutation.mutateAsync({ file: audioFile, storyId });
    },
    onSuccess: (audioUrl) => {
      toast({
        title: "音频生成并保存成功！",
        description: "AI生成的音频已保存，下次访问时可直接播放。",
      });
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "音频生成失败",
        description: error.message,
      });
    }
  });

  return {
    uploadAudio: uploadAudioMutation.mutate,
    isUploading: uploadAudioMutation.isPending,
    generateAndUploadAudio: generateAndUploadAudioMutation.mutate,
    isGenerating: generateAndUploadAudioMutation.isPending,
  };
};
