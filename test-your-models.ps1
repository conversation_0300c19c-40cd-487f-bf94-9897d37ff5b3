# Test Your Specific DashScope Models
# Usage: .\test-your-models.ps1 -Api<PERSON>ey "your-complete-api-key"

param(
    [Parameter(Mandatory=$true)]
    [string]$ApiKey
)

Write-Host "=== Testing Your Available Models ===" -ForegroundColor Cyan
Write-Host "API Key: $($ApiKey.Substring(0,10))..." -ForegroundColor Yellow

# Test Text Generation (Qwen)
function Test-QwenText {
    param([string]$ApiKey)
    
    Write-Host "`n[QWEN] Testing Qwen text generation..." -ForegroundColor Green
    
    $headers = @{
        'Authorization' = "Bearer $ApiKey"
        'Content-Type' = 'application/json'
    }

    $body = @{
        model = "qwen-turbo"
        input = @{
            messages = @(
                @{
                    role = "user"
                    content = "Hello, please write a short story about a cat."
                }
            )
        }
        parameters = @{
            temperature = 0.7
            max_tokens = 200
        }
    } | ConvertTo-Json -Depth 10

    try {
        $response = Invoke-RestMethod -Uri 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation' -Method POST -Headers $headers -Body $body -TimeoutSec 30
        
        $content = $response.output.text
        if ($content) {
            Write-Host "SUCCESS: Qwen text generation works!" -ForegroundColor Green
            Write-Host "Story preview: $($content.Substring(0, [Math]::Min(100, $content.Length)))..." -ForegroundColor White
            return $true
        } else {
            Write-Host "FAILED: No content returned" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "FAILED: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.ErrorDetails.Message) {
            $errorDetail = $_.ErrorDetails.Message | ConvertFrom-Json
            Write-Host "Error: $($errorDetail.code) - $($errorDetail.message)" -ForegroundColor Yellow
        }
        return $false
    }
}

# Test Qwen-TTS Audio Generation
function Test-QwenTTS {
    param([string]$ApiKey)

    Write-Host "`n[QWEN-TTS] Testing Qwen-TTS audio generation..." -ForegroundColor Green

    $headers = @{
        'Authorization' = "Bearer $ApiKey"
        'Content-Type' = 'application/json'
    }

    $body = @{
        model = "qwen-tts"
        text = "Hello, this is a test of Qwen TTS text to speech."
        voice = "Cherry"
    } | ConvertTo-Json -Depth 10

    try {
        $response = Invoke-RestMethod -Uri 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text2speech/speech-synthesis' -Method POST -Headers $headers -Body $body -TimeoutSec 30

        if ($response.output -and $response.output.audio -and $response.output.audio.url) {
            Write-Host "SUCCESS: Qwen-TTS works!" -ForegroundColor Green
            Write-Host "Audio URL: $($response.output.audio.url)" -ForegroundColor White
            return $true
        } else {
            Write-Host "FAILED: No audio URL returned" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "FAILED: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.ErrorDetails.Message) {
            $errorDetail = $_.ErrorDetails.Message | ConvertFrom-Json
            Write-Host "Error: $($errorDetail.code) - $($errorDetail.message)" -ForegroundColor Yellow
        }
        return $false
    }
}

# Test Wanx Image Generation
function Test-WanxImage {
    param([string]$ApiKey)
    
    Write-Host "`n[WANX] Testing Wanx image generation..." -ForegroundColor Green
    
    $headers = @{
        'Authorization' = "Bearer $ApiKey"
        'Content-Type' = 'application/json'
    }

    $body = @{
        model = "wanx-v1"
        input = @{
            prompt = "a cute cat sitting in a garden"
        }
        parameters = @{
            size = "1024*1024"
            n = 1
        }
    } | ConvertTo-Json -Depth 10

    try {
        $response = Invoke-RestMethod -Uri 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis' -Method POST -Headers $headers -Body $body -TimeoutSec 60
        
        if ($response.output -and $response.output.results) {
            Write-Host "SUCCESS: Wanx image generation works!" -ForegroundColor Green
            Write-Host "Image URL: $($response.output.results[0].url)" -ForegroundColor White
            return $true
        } else {
            Write-Host "FAILED: No image URL returned" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "FAILED: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.ErrorDetails.Message) {
            $errorDetail = $_.ErrorDetails.Message | ConvertFrom-Json
            Write-Host "Error: $($errorDetail.code) - $($errorDetail.message)" -ForegroundColor Yellow
        }
        return $false
    }
}

# Test FLUX Image Generation
function Test-FluxImage {
    param([string]$ApiKey)
    
    Write-Host "`n[FLUX] Testing FLUX image generation..." -ForegroundColor Green
    
    $headers = @{
        'Authorization' = "Bearer $ApiKey"
        'Content-Type' = 'application/json'
    }

    $models = @("flux-schnell", "flux-dev")
    
    foreach ($model in $models) {
        Write-Host "Trying FLUX model: $model" -ForegroundColor Yellow
        
        $body = @{
            model = $model
            input = @{
                prompt = "a beautiful landscape with mountains and lake"
            }
            parameters = @{
                size = "1024*1024"
                n = 1
            }
        } | ConvertTo-Json -Depth 10

        try {
            $response = Invoke-RestMethod -Uri 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis' -Method POST -Headers $headers -Body $body -TimeoutSec 60
            
            if ($response.output -and $response.output.results) {
                Write-Host "SUCCESS: FLUX $model works!" -ForegroundColor Green
                Write-Host "Image URL: $($response.output.results[0].url)" -ForegroundColor White
                return $true
            }
        } catch {
            Write-Host "FLUX $model failed: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    return $false
}

# Main Test
Write-Host "`nTesting your available models..." -ForegroundColor Cyan

$results = @{
    QwenText = Test-QwenText -ApiKey $ApiKey
    QwenTTS = Test-QwenTTS -ApiKey $ApiKey
    WanxImage = Test-WanxImage -ApiKey $ApiKey
    FluxImage = Test-FluxImage -ApiKey $ApiKey
}

# Results Summary
Write-Host "`n=== Your Model Test Results ===" -ForegroundColor Cyan
Write-Host "Qwen Text Generation: $(if($results.QwenText){'✅ WORKS'}else{'❌ FAILED'})" -ForegroundColor $(if($results.QwenText){'Green'}else{'Red'})
Write-Host "CosyVoice Audio: $(if($results.CosyVoice){'✅ WORKS'}else{'❌ FAILED'})" -ForegroundColor $(if($results.CosyVoice){'Green'}else{'Red'})
Write-Host "Wanx Image Generation: $(if($results.WanxImage){'✅ WORKS'}else{'❌ FAILED'})" -ForegroundColor $(if($results.WanxImage){'Green'}else{'Red'})
Write-Host "FLUX Image Generation: $(if($results.FluxImage){'✅ WORKS'}else{'❌ FAILED'})" -ForegroundColor $(if($results.FluxImage){'Green'}else{'Red'})

$workingCount = ($results.Values | Where-Object {$_}).Count
Write-Host "`nWorking Services: $workingCount/4" -ForegroundColor $(if($workingCount -gt 2){'Green'}elseif($workingCount -gt 0){'Yellow'}else{'Red'})

# Configuration Recommendations
Write-Host "`n=== Recommended App Configuration ===" -ForegroundColor Cyan

if ($results.QwenText) {
    Write-Host "✅ Text Generation: Use 'qwen-turbo'" -ForegroundColor Green
} else {
    Write-Host "❌ Text Generation: Check API key" -ForegroundColor Red
}

if ($results.CosyVoice) {
    Write-Host "✅ Audio Generation: Use 'cosyvoice-v1'" -ForegroundColor Green
} else {
    Write-Host "⚠️  Audio Generation: Use browser TTS fallback" -ForegroundColor Yellow
}

if ($results.WanxImage -or $results.FluxImage) {
    $imageModel = if ($results.WanxImage) { "wanx-v1" } else { "flux-schnell" }
    Write-Host "✅ Image Generation: Use '$imageModel'" -ForegroundColor Green
} else {
    Write-Host "⚠️  Image Generation: Disable in app settings" -ForegroundColor Yellow
}

Write-Host "`nNext Steps:" -ForegroundColor Cyan
if ($workingCount -eq 0) {
    Write-Host "1. ⚠️  CRITICAL: Check your API key - it seems invalid" -ForegroundColor Red
    Write-Host "2. Get a new API key from DashScope console" -ForegroundColor White
    Write-Host "3. Make sure you copy the COMPLETE API key" -ForegroundColor White
} else {
    Write-Host "1. ✅ Configure your app with the working services" -ForegroundColor Green
    Write-Host "2. 🔧 Update your app configuration accordingly" -ForegroundColor White
    Write-Host "3. 🚀 Start creating multimodal stories!" -ForegroundColor White
}

Write-Host "`nTest completed!" -ForegroundColor Cyan
