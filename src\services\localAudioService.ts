// 本地音频生成服务 - 支持多种 TTS 提供商
export interface AudioGenerationConfig {
  provider?: 'minimax' | 'alicloud' | 'browser';
  apiKey: string;
  groupId?: string; // MiniMax 专用
  voiceId?: string;
  speed?: number;
  volume?: number;
  pitch?: number;
  region?: string; // 阿里云专用
}

export interface AudioGenerationResult {
  audioBlob: Blob;
  format: string;
  duration?: number;
}

class LocalAudioService {
  private config: AudioGenerationConfig | null = null;

  // 设置 MiniMax 配置
  setConfig(config: AudioGenerationConfig) {
    this.config = config;
  }

  // 检查配置是否有效
  isConfigured(): boolean {
    return !!(this.config?.apiKey && this.config?.groupId);
  }

  // 生成音频 - 支持多种提供商
  async generateAudio(text: string): Promise<AudioGenerationResult> {
    if (!this.config) {
      throw new Error('音频配置未设置，请先调用 setConfig()');
    }

    if (!text.trim()) {
      throw new Error('文本内容不能为空');
    }

    const provider = this.config.provider || 'minimax';

    switch (provider) {
      case 'minimax':
        return await this.generateAudioWithMiniMax(text);
      case 'alicloud':
        return await this.generateAudioWithAliCloud(text);
      case 'browser':
        return await this.generateAudioWithBrowserTTS(text);
      default:
        throw new Error(`不支持的音频提供商: ${provider}`);
    }
  }

  // 使用 MiniMax API 生成音频
  private async generateAudioWithMiniMax(text: string): Promise<AudioGenerationResult> {
    if (!this.config?.groupId) {
      throw new Error('MiniMax 配置不完整，缺少 Group ID');
    }

    try {
      console.log('正在调用 MiniMax TTS API...');

      const response = await fetch(`https://api.minimaxi.com/v1/t2a_v2?GroupId=${this.config.groupId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`,
        },
        body: JSON.stringify({
          model: "speech-02-hd",
          text: text,
          stream: false,
          output_format: "hex",
          voice_setting: {
            voice_id: this.config.voiceId || "male-qn-qingse",
            speed: this.config.speed || 1,
            vol: this.config.volume || 1,
            pitch: this.config.pitch || 0,
          },
          audio_setting: {
            sample_rate: 32000,
            bitrate: 128000,
            format: "mp3"
          }
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`MiniMax API 调用失败. 状态: ${response.status}`);
      }

      const responseData = await response.json();

      if (responseData.base_resp?.status_code !== 0) {
        throw new Error(`MiniMax API 错误: ${responseData.base_resp?.status_msg || '未知错误'}`);
      }

      const hexAudio = responseData.data?.audio;
      const format = responseData.extra_info?.audio_format || 'mp3';

      if (!hexAudio) {
        throw new Error('从 MiniMax 获取音频失败');
      }

      const audioBytes = this.hexToBytes(hexAudio);
      const audioBlob = new Blob([audioBytes], { type: `audio/${format}` });

      return {
        audioBlob,
        format,
        duration: responseData.extra_info?.audio_length
      };

    } catch (error) {
      console.error('MiniMax 音频生成失败:', error);
      throw error;
    }
  }

  // 使用阿里云 Qwen-TTS 生成音频
  private async generateAudioWithAliCloud(text: string): Promise<AudioGenerationResult> {
    try {
      console.log('正在调用阿里云 Qwen-TTS API...');

      const response = await fetch('https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config!.apiKey}`,
        },
        body: JSON.stringify({
          model: 'qwen-tts',
          input: {
            text: text,
            voice: this.config!.voiceId || 'Cherry'
          }
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`阿里云 TTS API 调用失败. 状态: ${response.status}`);
      }

      const responseData = await response.json();

      if (responseData.code && responseData.code !== '200') {
        throw new Error(`阿里云 TTS API 错误: ${responseData.message || responseData.code}`);
      }

      if (responseData.output?.audio?.url) {
        // 下载音频文件
        const audioResponse = await fetch(responseData.output.audio.url);
        if (!audioResponse.ok) {
          throw new Error('音频下载失败');
        }

        const audioBlob = await audioResponse.blob();
        return {
          audioBlob,
          format: 'wav',
          duration: responseData.usage?.output_tokens
        };
      } else {
        throw new Error('阿里云 TTS 未返回音频URL');
      }

    } catch (error) {
      console.error('阿里云 TTS 音频生成失败:', error);
      throw error;
    }
  }

  // 使用浏览器原生 TTS 作为备选方案
  async generateAudioWithBrowserTTS(text: string): Promise<AudioGenerationResult> {
    return new Promise((resolve, reject) => {
      if (!('speechSynthesis' in window)) {
        reject(new Error('浏览器不支持语音合成'));
        return;
      }

      try {
        const utterance = new SpeechSynthesisUtterance(text);
        
        // 设置语音参数
        utterance.lang = 'en-US';
        utterance.rate = 0.9;
        utterance.pitch = 1;
        utterance.volume = 1;

        // 尝试使用英语语音
        const voices = speechSynthesis.getVoices();
        const englishVoice = voices.find(voice => 
          voice.lang.startsWith('en') && voice.name.includes('Google')
        ) || voices.find(voice => voice.lang.startsWith('en'));
        
        if (englishVoice) {
          utterance.voice = englishVoice;
        }

        utterance.onend = () => {
          // 浏览器 TTS 不能直接生成音频文件，这里返回一个空的 Blob
          // 实际使用时会直接播放，不需要 Blob
          const emptyBlob = new Blob([], { type: 'audio/wav' });
          resolve({
            audioBlob: emptyBlob,
            format: 'wav'
          });
        };

        utterance.onerror = (event) => {
          reject(new Error(`浏览器 TTS 错误: ${event.error}`));
        };

        speechSynthesis.speak(utterance);
      } catch (error) {
        reject(error);
      }
    });
  }

  // 直接播放文本（使用浏览器 TTS）
  async playTextDirectly(text: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!('speechSynthesis' in window)) {
        reject(new Error('浏览器不支持语音合成'));
        return;
      }

      try {
        // 停止当前播放
        speechSynthesis.cancel();

        const utterance = new SpeechSynthesisUtterance(text);
        
        // 设置语音参数
        utterance.lang = 'en-US';
        utterance.rate = 0.9;
        utterance.pitch = 1;
        utterance.volume = 1;

        // 选择英语语音
        const voices = speechSynthesis.getVoices();
        const englishVoice = voices.find(voice => 
          voice.lang.startsWith('en') && (voice.name.includes('Google') || voice.name.includes('Microsoft'))
        ) || voices.find(voice => voice.lang.startsWith('en'));
        
        if (englishVoice) {
          utterance.voice = englishVoice;
        }

        utterance.onend = () => resolve();
        utterance.onerror = (event) => reject(new Error(`语音播放错误: ${event.error}`));

        speechSynthesis.speak(utterance);
      } catch (error) {
        reject(error);
      }
    });
  }

  // 停止当前播放
  stopPlayback(): void {
    if ('speechSynthesis' in window) {
      speechSynthesis.cancel();
    }
  }

  // 获取可用的语音列表
  getAvailableVoices(): SpeechSynthesisVoice[] {
    if ('speechSynthesis' in window) {
      return speechSynthesis.getVoices();
    }
    return [];
  }

  // 十六进制字符串转字节数组
  private hexToBytes(hex: string): Uint8Array {
    const bytes = new Uint8Array(hex.length / 2);
    for (let i = 0; i < hex.length; i += 2) {
      bytes[i / 2] = parseInt(hex.substring(i, i + 2), 16);
    }
    return bytes;
  }

  // 检查 MiniMax API 连接
  async testConnection(): Promise<boolean> {
    if (!this.config) {
      return false;
    }

    try {
      const result = await this.generateAudio('Hello, this is a test.');
      return result.audioBlob.size > 0;
    } catch (error) {
      console.error('MiniMax 连接测试失败:', error);
      return false;
    }
  }
}

// 创建单例实例
export const localAudioService = new LocalAudioService();

// 导出类型和服务
export default LocalAudioService;
