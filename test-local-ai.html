<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地 AI 服务测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <h1>本地 AI 服务测试</h1>
    
    <div class="grid">
        <!-- AI 配置 -->
        <div class="container">
            <h2>AI 服务配置</h2>
            <div class="form-group">
                <label for="provider">AI 提供商</label>
                <select id="provider" onchange="updateModelOptions()">
                    <option value="alicloud">阿里云 (推荐)</option>
                    <option value="minimax">MiniMax</option>
                    <option value="siliconflow">SiliconFlow</option>
                    <option value="openai">OpenAI</option>
                    <option value="ollama">Ollama (本地)</option>
                </select>
            </div>
            <div class="form-group">
                <label for="apiKey">API Key</label>
                <input type="password" id="apiKey" placeholder="输入 API Key">
            </div>
            <div class="form-group" id="groupIdGroup">
                <label for="groupId">Group ID (MiniMax)</label>
                <input type="text" id="groupId" placeholder="输入 Group ID">
            </div>
            <div class="form-group" id="baseUrlGroup" style="display: none;">
                <label for="baseUrl">Base URL (Ollama)</label>
                <input type="text" id="baseUrl" value="http://localhost:11434" placeholder="输入 Base URL">
            </div>
            <div class="form-group">
                <label for="model">模型</label>
                <select id="model">
                    <option value="abab6.5s-chat">abab6.5s-chat</option>
                </select>
            </div>
            <button onclick="saveAIConfig()">保存 AI 配置</button>
            <button onclick="testAIConnection()">测试 AI 连接</button>
            <div id="aiStatus"></div>
        </div>

        <!-- 音频配置 -->
        <div class="container">
            <h2>音频服务配置 (MiniMax)</h2>
            <div class="form-group">
                <label for="audioApiKey">API Key</label>
                <input type="password" id="audioApiKey" placeholder="输入 MiniMax API Key">
            </div>
            <div class="form-group">
                <label for="audioGroupId">Group ID</label>
                <input type="text" id="audioGroupId" placeholder="输入 Group ID">
            </div>
            <div class="form-group">
                <label for="voiceId">语音 ID</label>
                <select id="voiceId">
                    <option value="male-qn-qingse">男声-青涩</option>
                    <option value="female-shaonv">女声-少女</option>
                    <option value="male-qn-jingying">男声-精英</option>
                    <option value="female-qn-wenwen">女声-温文</option>
                </select>
            </div>
            <button onclick="saveAudioConfig()">保存音频配置</button>
            <button onclick="testAudioConnection()">测试音频连接</button>
            <div id="audioStatus"></div>
        </div>
    </div>

    <!-- 功能测试 -->
    <div class="container">
        <h2>功能测试</h2>
        
        <h3>故事生成测试</h3>
        <div class="form-group">
            <label for="storyPrompt">故事提示</label>
            <textarea id="storyPrompt" rows="3" placeholder="例如：一只想去月球旅行的勇敢小狐狸"></textarea>
        </div>
        <button onclick="testStoryGeneration()" id="generateBtn">生成故事</button>
        <div id="storyResult"></div>

        <h3>音频生成测试</h3>
        <div class="form-group">
            <label for="audioText">测试文本</label>
            <textarea id="audioText" rows="2" placeholder="Hello, this is a test for text-to-speech.">Hello, this is a test for text-to-speech.</textarea>
        </div>
        <button onclick="testAudioGeneration()" id="audioBtn">生成音频</button>
        <button onclick="testBrowserTTS()" id="browserTTSBtn">测试浏览器 TTS</button>
        <div id="audioResult"></div>
        <audio id="audioPlayer" controls style="width: 100%; margin-top: 10px; display: none;"></audio>
    </div>

    <script>
        // 配置管理
        let aiConfig = {};
        let audioConfig = {};

        // 更新模型选项
        function updateModelOptions() {
            const provider = document.getElementById('provider').value;
            const modelSelect = document.getElementById('model');
            const groupIdGroup = document.getElementById('groupIdGroup');
            const baseUrlGroup = document.getElementById('baseUrlGroup');
            
            // 显示/隐藏特定字段
            if (provider === 'minimax') {
                groupIdGroup.style.display = 'block';
                baseUrlGroup.style.display = 'none';
            } else if (provider === 'ollama') {
                groupIdGroup.style.display = 'none';
                baseUrlGroup.style.display = 'block';
            } else {
                groupIdGroup.style.display = 'none';
                baseUrlGroup.style.display = 'none';
            }
            
            // 更新模型选项
            modelSelect.innerHTML = '';
            const models = {
                'alicloud': ['qwen2.5-omni-7b', 'qwen-turbo', 'qwen-plus', 'qwen-max', 'qwen-max-longcontext'],
                'minimax': ['abab6.5s-chat', 'abab6.5g-chat', 'abab5.5s-chat'],
                'siliconflow': ['Qwen/Qwen2.5-7B-Instruct', 'Qwen/Qwen2.5-14B-Instruct'],
                'openai': ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo'],
                'ollama': ['llama2', 'llama3', 'qwen2', 'gemma']
            };
            
            models[provider].forEach(model => {
                const option = document.createElement('option');
                option.value = model;
                option.textContent = model;
                modelSelect.appendChild(option);
            });
        }

        // 保存 AI 配置
        function saveAIConfig() {
            aiConfig = {
                provider: document.getElementById('provider').value,
                apiKey: document.getElementById('apiKey').value,
                groupId: document.getElementById('groupId').value,
                baseUrl: document.getElementById('baseUrl').value,
                model: document.getElementById('model').value
            };
            
            localStorage.setItem('test_ai_config', JSON.stringify(aiConfig));
            showStatus('aiStatus', 'success', 'AI 配置已保存');
        }

        // 保存音频配置
        function saveAudioConfig() {
            audioConfig = {
                apiKey: document.getElementById('audioApiKey').value,
                groupId: document.getElementById('audioGroupId').value,
                voiceId: document.getElementById('voiceId').value
            };
            
            localStorage.setItem('test_audio_config', JSON.stringify(audioConfig));
            showStatus('audioStatus', 'success', '音频配置已保存');
        }

        // 测试 AI 连接
        async function testAIConnection() {
            if (!aiConfig.apiKey && aiConfig.provider !== 'ollama') {
                showStatus('aiStatus', 'error', '请先配置 API Key');
                return;
            }
            
            showStatus('aiStatus', 'loading', '正在测试 AI 连接...');
            
            try {
                const response = await callAI('Hello, this is a test message.');
                showStatus('aiStatus', 'success', `AI 连接成功！响应: ${response.substring(0, 100)}...`);
            } catch (error) {
                showStatus('aiStatus', 'error', `AI 连接失败: ${error.message}`);
            }
        }

        // 测试音频连接
        async function testAudioConnection() {
            if (!audioConfig.apiKey) {
                showStatus('audioStatus', 'error', '请先配置音频 API Key');
                return;
            }
            
            showStatus('audioStatus', 'loading', '正在测试音频连接...');
            
            try {
                const audioBlob = await generateAudio('Hello, this is a test.');
                showStatus('audioStatus', 'success', `音频连接成功！生成了 ${audioBlob.size} 字节的音频`);
            } catch (error) {
                showStatus('audioStatus', 'error', `音频连接失败: ${error.message}`);
            }
        }

        // 测试故事生成
        async function testStoryGeneration() {
            const prompt = document.getElementById('storyPrompt').value;
            if (!prompt.trim()) {
                showStatus('storyResult', 'error', '请输入故事提示');
                return;
            }
            
            const btn = document.getElementById('generateBtn');
            btn.disabled = true;
            btn.textContent = '生成中...';
            showStatus('storyResult', 'loading', '正在生成故事...');
            
            try {
                const story = await generateStory(prompt);
                document.getElementById('storyResult').innerHTML = `
                    <div class="result">
                        <h4>${story.title}</h4>
                        <p><strong>描述:</strong> ${story.description}</p>
                        <p><strong>故事:</strong></p>
                        <div>${story.story}</div>
                        ${story.storyChinese ? `<p><strong>中文翻译:</strong></p><div>${story.storyChinese}</div>` : ''}
                    </div>
                `;
            } catch (error) {
                showStatus('storyResult', 'error', `故事生成失败: ${error.message}`);
            } finally {
                btn.disabled = false;
                btn.textContent = '生成故事';
            }
        }

        // 测试音频生成
        async function testAudioGeneration() {
            const text = document.getElementById('audioText').value;
            if (!text.trim()) {
                showStatus('audioResult', 'error', '请输入测试文本');
                return;
            }
            
            const btn = document.getElementById('audioBtn');
            btn.disabled = true;
            btn.textContent = '生成中...';
            showStatus('audioResult', 'loading', '正在生成音频...');
            
            try {
                const audioBlob = await generateAudio(text);
                const audioUrl = URL.createObjectURL(audioBlob);
                const audioPlayer = document.getElementById('audioPlayer');
                audioPlayer.src = audioUrl;
                audioPlayer.style.display = 'block';
                showStatus('audioResult', 'success', `音频生成成功！大小: ${audioBlob.size} 字节`);
            } catch (error) {
                showStatus('audioResult', 'error', `音频生成失败: ${error.message}`);
            } finally {
                btn.disabled = false;
                btn.textContent = '生成音频';
            }
        }

        // 测试浏览器 TTS
        function testBrowserTTS() {
            const text = document.getElementById('audioText').value;
            if (!text.trim()) {
                showStatus('audioResult', 'error', '请输入测试文本');
                return;
            }
            
            if (!('speechSynthesis' in window)) {
                showStatus('audioResult', 'error', '浏览器不支持语音合成');
                return;
            }
            
            speechSynthesis.cancel();
            const utterance = new SpeechSynthesisUtterance(text);
            utterance.lang = 'en-US';
            utterance.rate = 0.9;
            
            utterance.onend = () => {
                showStatus('audioResult', 'success', '浏览器 TTS 播放完成');
            };
            
            utterance.onerror = (event) => {
                showStatus('audioResult', 'error', `浏览器 TTS 错误: ${event.error}`);
            };
            
            speechSynthesis.speak(utterance);
            showStatus('audioResult', 'info', '正在使用浏览器 TTS 播放...');
        }

        // AI 调用函数
        async function callAI(prompt) {
            const config = aiConfig;
            
            if (config.provider === 'alicloud') {
                return await callAliCloudAPI(prompt);
            } else if (config.provider === 'minimax') {
                return await callMiniMaxAPI(prompt);
            } else if (config.provider === 'siliconflow') {
                return await callSiliconFlowAPI(prompt);
            } else {
                throw new Error(`不支持的 AI 提供商: ${config.provider}`);
            }
        }

        // MiniMax API 调用
        async function callMiniMaxAPI(prompt) {
            const response = await fetch(`https://api.minimaxi.com/v1/text/chatcompletion_v2?GroupId=${aiConfig.groupId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${aiConfig.apiKey}`,
                },
                body: JSON.stringify({
                    model: aiConfig.model,
                    messages: [{ role: 'user', content: prompt }],
                    stream: false,
                    temperature: 0.7,
                    max_tokens: 2000,
                }),
            });

            if (!response.ok) {
                throw new Error(`MiniMax API 错误: ${response.status}`);
            }

            const data = await response.json();
            
            if (data.base_resp?.status_code !== 0) {
                throw new Error(`MiniMax API 错误: ${data.base_resp?.status_msg}`);
            }

            return data.choices[0]?.message?.content || '';
        }

        // 阿里云 API 调用
        async function callAliCloudAPI(prompt) {
            const response = await fetch('https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${aiConfig.apiKey}`,
                },
                body: JSON.stringify({
                    model: aiConfig.model,
                    input: {
                        messages: [{ role: 'user', content: prompt }]
                    },
                    parameters: {
                        temperature: 0.7,
                        max_tokens: 2000,
                        top_p: 0.8
                    }
                }),
            });

            if (!response.ok) {
                throw new Error(`阿里云 API 错误: ${response.status}`);
            }

            const data = await response.json();

            if (data.code && data.code !== '200') {
                throw new Error(`阿里云 API 错误: ${data.message || data.code}`);
            }

            return data.output?.choices?.[0]?.message?.content || '';
        }

        // SiliconFlow API 调用
        async function callSiliconFlowAPI(prompt) {
            const response = await fetch('https://api.siliconflow.cn/v1/chat/completions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${aiConfig.apiKey}`,
                },
                body: JSON.stringify({
                    model: aiConfig.model,
                    messages: [{ role: 'user', content: prompt }],
                    stream: false,
                    temperature: 0.7,
                    max_tokens: 2000,
                }),
            });

            if (!response.ok) {
                throw new Error(`SiliconFlow API 错误: ${response.status}`);
            }

            const data = await response.json();
            return data.choices[0]?.message?.content || '';
        }

        // 故事生成
        async function generateStory(prompt) {
            const storyPrompt = `请根据以下提示创作一个英文故事，并提供中文翻译：

提示：${prompt}

请按照以下JSON格式返回：
{
  "title": "故事标题（英文）",
  "description": "故事简介（中文，50字以内）",
  "story": "完整的英文故事（200-500词）",
  "storyChinese": "中文翻译"
}`;

            const response = await callAI(storyPrompt);
            
            try {
                const jsonMatch = response.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    return JSON.parse(jsonMatch[0]);
                }
            } catch (error) {
                console.warn('JSON 解析失败，使用文本解析');
            }
            
            return {
                title: '生成的故事',
                description: '一个由 AI 生成的有趣故事',
                story: response,
                storyChinese: undefined
            };
        }

        // 音频生成
        async function generateAudio(text) {
            const response = await fetch(`https://api.minimaxi.com/v1/t2a_v2?GroupId=${audioConfig.groupId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${audioConfig.apiKey}`,
                },
                body: JSON.stringify({
                    model: "speech-02-hd",
                    text: text,
                    stream: false,
                    output_format: "hex",
                    voice_setting: {
                        voice_id: audioConfig.voiceId,
                        speed: 1,
                        vol: 1,
                        pitch: 0,
                    },
                    audio_setting: {
                        sample_rate: 32000,
                        bitrate: 128000,
                        format: "mp3"
                    }
                }),
            });

            if (!response.ok) {
                throw new Error(`MiniMax TTS API 错误: ${response.status}`);
            }

            const data = await response.json();
            
            if (data.base_resp?.status_code !== 0) {
                throw new Error(`MiniMax TTS 错误: ${data.base_resp?.status_msg}`);
            }

            const hexAudio = data.data?.audio;
            if (!hexAudio) {
                throw new Error('没有收到音频数据');
            }

            // 转换十六进制为字节数组
            const bytes = new Uint8Array(hexAudio.length / 2);
            for (let i = 0; i < hexAudio.length; i += 2) {
                bytes[i / 2] = parseInt(hexAudio.substr(i, 2), 16);
            }

            return new Blob([bytes], { type: 'audio/mp3' });
        }

        // 显示状态
        function showStatus(elementId, type, message) {
            const element = document.getElementById(elementId);
            element.className = `status ${type}`;
            element.textContent = message;
        }

        // 页面加载时恢复配置
        window.onload = function() {
            const savedAiConfig = localStorage.getItem('test_ai_config');
            const savedAudioConfig = localStorage.getItem('test_audio_config');
            
            if (savedAiConfig) {
                aiConfig = JSON.parse(savedAiConfig);
                document.getElementById('provider').value = aiConfig.provider || 'minimax';
                document.getElementById('apiKey').value = aiConfig.apiKey || '';
                document.getElementById('groupId').value = aiConfig.groupId || '';
                document.getElementById('baseUrl').value = aiConfig.baseUrl || 'http://localhost:11434';
                updateModelOptions();
                document.getElementById('model').value = aiConfig.model || '';
            }
            
            if (savedAudioConfig) {
                audioConfig = JSON.parse(savedAudioConfig);
                document.getElementById('audioApiKey').value = audioConfig.apiKey || '';
                document.getElementById('audioGroupId').value = audioConfig.groupId || '';
                document.getElementById('voiceId').value = audioConfig.voiceId || 'male-qn-qingse';
            }
            
            updateModelOptions();
        };
    </script>
</body>
</html>
