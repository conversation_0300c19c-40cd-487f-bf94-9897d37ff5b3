
import React from 'react';
import { FileText, Edit3, Mic, BookOpen } from 'lucide-react';

interface PracticeModeSelectorProps {
  currentMode: 'listening' | 'dictation' | 'fillBlank' | 'speaking';
  onModeChange: (mode: 'listening' | 'dictation' | 'fillBlank' | 'speaking') => void;
}

const PracticeModeSelector = ({ currentMode, onModeChange }: PracticeModeSelectorProps) => {
  const modes = [
    { id: 'listening' as const, name: '听力理解', icon: FileText, color: 'blue' },
    { id: 'dictation' as const, name: '听写模式', icon: Edit3, color: 'green' },
    { id: 'fillBlank' as const, name: '填空练习', icon: BookOpen, color: 'purple' },
    { id: 'speaking' as const, name: '跟读练习', icon: Mic, color: 'red' }
  ];

  return (
    <div className="bg-white rounded-2xl shadow-lg mb-6 p-6">
      <h3 className="text-xl font-semibold text-gray-900 mb-4">练习模式</h3>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {modes.map((mode) => {
          const Icon = mode.icon;
          const isActive = currentMode === mode.id;
          return (
            <button
              key={mode.id}
              onClick={() => onModeChange(mode.id)}
              className={`p-4 rounded-xl border-2 transition-all duration-200 ${
                isActive
                  ? `border-${mode.color}-500 bg-${mode.color}-50`
                  : 'border-gray-200 hover:border-gray-300 bg-gray-50'
              }`}
            >
              <Icon
                size={24}
                className={`mx-auto mb-2 ${
                  isActive ? `text-${mode.color}-600` : 'text-gray-600'
                }`}
              />
              <div className={`text-sm font-medium ${
                isActive ? `text-${mode.color}-700` : 'text-gray-700'
              }`}>
                {mode.name}
              </div>
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default PracticeModeSelector;
