import React, { useState, useRef } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { toast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Trash2, Volume2, Loader2, BookOpen } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';

interface WordDefinition {
  word: string;
  definition: string;
  partOfSpeech: string;
  example: string;
  phonetic?: string;
  chineseDefinition?: string;
  chineseExample?: string;
  baseForm?: string;
}

interface VocabularyWord {
  id: string;
  word: string;
  context: string | null;
  definition: WordDefinition | null;
  created_at: string;
}

const VocabularyList = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [playingId, setPlayingId] = useState<string | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  const { data: words, isLoading, error } = useQuery<VocabularyWord[]>({
    queryKey: ['vocabulary', user?.id],
    queryFn: async () => {
      if (!user) return [];
      const { data, error } = await supabase
        .from('vocabulary')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        throw new Error(error.message);
      }
      return data as any;
    },
    enabled: !!user,
  });

  const deleteMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase.from('vocabulary').delete().eq('id', id);
      if (error) throw new Error(error.message);
    },
    onSuccess: () => {
      toast({ title: '删除成功' });
      queryClient.invalidateQueries({ queryKey: ['vocabulary'] });
    },
    onError: (error) => {
      toast({ variant: 'destructive', title: '删除失败', description: error.message });
    },
  });
  
  const playTextToSpeech = async (text: string, wordId: string) => {
    if (playingId) {
      if (audioRef.current) audioRef.current.pause();
      setPlayingId(null);
      if (playingId === wordId) return;
    }

    setPlayingId(wordId);
    try {
      const { data, error } = await supabase.functions.invoke('text-to-speech', {
        body: { text },
      });

      if (error || !data.audioContent) {
        throw new Error(error?.message || "无法生成音频");
      }

      const audioFormat = data.format || 'mp3';
      const audioSrc = `data:audio/${audioFormat};base64,${data.audioContent}`;
      audioRef.current = new Audio(audioSrc);
      audioRef.current.play();
      audioRef.current.onended = () => setPlayingId(null);
      audioRef.current.onerror = () => {
        toast({ variant: 'destructive', title: '播放失败' });
        setPlayingId(null);
      }
    } catch (err: any) {
      toast({ variant: 'destructive', title: '播放失败', description: err.message });
      setPlayingId(null);
    }
  }

  if (isLoading) {
    return <div className="flex items-center justify-center h-full"><Loader2 className="h-8 w-8 animate-spin" /></div>;
  }

  if (error) {
    return <div className="text-red-500 text-center">加载生词本失败: {error.message}</div>;
  }

  if (!words || words.length === 0) {
    return (
        <div className="flex flex-col items-center justify-center h-full text-center">
            <BookOpen className="w-16 h-16 text-gray-300 mb-4" />
            <h3 className="text-xl font-semibold text-gray-700">生词本是空的</h3>
            <p className="text-gray-500 mt-2">在和 AI 对话时，点击单词可以将它收藏到这里哦。</p>
        </div>
    );
  }

  return (
    <ScrollArea className="h-full">
      <Accordion type="multiple" className="w-full">
        {words.map((word) => (
          <AccordionItem value={word.id} key={word.id}>
            <AccordionTrigger className="text-lg font-semibold hover:no-underline">
              <span className="flex items-center gap-4">
                {word.word}
                {word.definition?.partOfSpeech && <Badge variant="outline">{word.definition.partOfSpeech}</Badge>}
              </span>
            </AccordionTrigger>
            <AccordionContent className="space-y-4 pt-2">
              {word.definition ? (
                <div className="space-y-3 text-sm">
                  <div className="flex items-center gap-2">
                    <p className="font-semibold text-gray-800">{word.definition.chineseDefinition}</p>
                    {word.definition.phonetic && <p className="text-gray-500">[{word.definition.phonetic}]</p>}
                  </div>

                  <p className="text-gray-700">{word.definition.definition}</p>
                  
                  <div className="text-xs text-gray-600 italic mt-2 p-2 bg-gray-50 rounded border">
                    <p>例: {word.definition.example}</p>
                    <p>{word.definition.chineseExample}</p>
                  </div>

                  {word.definition.baseForm && word.word.toLowerCase() !== word.definition.baseForm.toLowerCase() && (
                    <p className="text-xs text-gray-500">基本形态: {word.definition.baseForm}</p>
                  )}

                  {word.context && (
                    <Alert className="mt-4">
                      <AlertTitle className="text-xs font-semibold">原文上下文</AlertTitle>
                      <AlertDescription className="text-sm">"{word.context}"</AlertDescription>
                    </Alert>
                  )}
                </div>
              ) : (
                <p className="text-gray-500">暂无详细释义。</p>
              )}

              <div className="flex justify-end space-x-2 pt-2">
                <Button variant="outline" size="icon" onClick={() => playTextToSpeech(word.word, word.id)} disabled={!!playingId}>
                  {playingId === word.id ? <Loader2 className="h-4 w-4 animate-spin" /> : <Volume2 className="h-4 w-4" />}
                </Button>
                <Button
                  variant="destructive"
                  size="icon"
                  onClick={() => deleteMutation.mutate(word.id)}
                  disabled={deleteMutation.isPending}
                >
                  {deleteMutation.isPending ? <Loader2 className="h-4 w-4 animate-spin" /> : <Trash2 className="h-4 w-4" />}
                </Button>
              </div>
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </ScrollArea>
  );
};

export default VocabularyList;
