
import React, { useState, useMemo, useEffect, useRef } from 'react';
import { CheckCircle, XCircle, RotateCcw, ArrowRight, Volume2, Loader2 } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

interface DictationModeProps {
  transcript: string;
  onComplete: (score: number, userInput: string) => void;
  onReset: () => void;
}

const DictationMode = ({ transcript, onComplete, onReset }: DictationModeProps) => {
  const sentences = useMemo(() => {
    // Split by sentence-ending punctuation, keeping the punctuation. Handles English and CJK punctuation.
    return transcript
      .split(/(?<=[.!?。？！])\s*/)
      .map(s => s.trim())
      .filter(s => s.length > 0);
  }, [transcript]);

  const [currentSentenceIndex, setCurrentSentenceIndex] = useState(0);
  const [userInput, setUserInput] = useState('');
  const [results, setResults] = useState<{ userInput: string; score: number; original: string; }[]>([]);
  const [isCurrentSubmitted, setIsCurrentSubmitted] = useState(false);
  const [currentSentenceAudio, setCurrentSentenceAudio] = useState<string | null>(null);
  const [isFetchingAudio, setIsFetchingAudio] = useState(false);
  const audioRef = useRef<HTMLAudioElement>(null);

  const isFinished = currentSentenceIndex >= sentences.length;

  useEffect(() => {
    // Cleanup function to revoke the object URL to prevent memory leaks
    return () => {
      if (currentSentenceAudio) {
        URL.revokeObjectURL(currentSentenceAudio);
      }
    };
  }, [currentSentenceAudio]);

  useEffect(() => {
    if (isFinished || !sentences[currentSentenceIndex]) return;

    const fetchAudioForSentence = async () => {
      setIsFetchingAudio(true);
      
      const sentence = sentences[currentSentenceIndex];
      try {
        const { data, error } = await supabase.functions.invoke('text-to-speech', {
          body: { text: sentence, voice: 'nova' },
        });

        if (error) throw error;

        if (data.audioContent) {
          const byteCharacters = atob(data.audioContent);
          const byteNumbers = new Array(byteCharacters.length);
          for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
          }
          const byteArray = new Uint8Array(byteNumbers);
          const blob = new Blob([byteArray], { type: 'audio/mpeg' });
          const url = URL.createObjectURL(blob);
          setCurrentSentenceAudio(url);
        }
      } catch (err) {
        console.error('Error fetching sentence audio:', err);
      } finally {
        setIsFetchingAudio(false);
      }
    };

    fetchAudioForSentence();
  }, [currentSentenceIndex, sentences, isFinished]);
  
  const playCurrentSentence = () => {
    if (audioRef.current) {
      audioRef.current.play();
    }
  };

  const calculateSimilarity = (str1: string, str2: string): number => {
    const words1 = str1.split(/\s+/);
    const words2 = str2.split(/\s+/);
    
    let matches = 0;
    const maxLength = Math.max(words1.length, words2.length);
    
    for (let i = 0; i < Math.min(words1.length, words2.length); i++) {
      if (words1[i] === words2[i]) {
        matches++;
      }
    }
    
    return maxLength > 0 ? matches / maxLength : 0;
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const handleSubmitCurrent = () => {
    if (!userInput.trim()) return;

    const currentSentence = sentences[currentSentenceIndex];
    const similarity = calculateSimilarity(userInput.toLowerCase().trim(), currentSentence.toLowerCase().trim());
    const score = Math.round(similarity * 100);
    
    setResults(prev => [...prev, { userInput, score, original: currentSentence }]);
    setIsCurrentSubmitted(true);
  };

  const handleNext = () => {
    if (currentSentenceIndex < sentences.length - 1) {
      setCurrentSentenceIndex(prev => prev + 1);
      setUserInput('');
      setIsCurrentSubmitted(false);
    } else {
      const finalScore = results.length > 0 ? Math.round(results.reduce((acc, r) => acc + r.score, 0) / results.length) : 0;
      const allUserInput = results.map(r => r.userInput).join(' ');
      onComplete(finalScore, allUserInput);
      setCurrentSentenceIndex(prev => prev + 1);
    }
  };

  const handleReset = () => {
    setCurrentSentenceIndex(0);
    setUserInput('');
    setResults([]);
    setIsCurrentSubmitted(false);
    onReset();
  };

  if (isFinished) {
    const averageScore = results.length > 0 ? Math.round(results.reduce((acc, r) => acc + r.score, 0) / results.length) : 0;

    return (
      <div className="bg-white rounded-2xl shadow-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-semibold text-gray-900">听写练习完成</h3>
          <button onClick={handleReset} className="text-gray-500 hover:text-gray-700 transition-colors">
            <RotateCcw size={20} />
          </button>
        </div>
        <div className="text-center mb-6">
          <p className="text-lg text-gray-600">总平均分</p>
          <p className={`text-5xl font-bold ${getScoreColor(averageScore)}`}>{averageScore}%</p>
        </div>

        <div className="space-y-4 max-h-96 overflow-y-auto pr-2">
          {results.map((result, index) => (
            <div key={index} className="bg-gray-50 rounded-lg p-4">
              <div className="flex justify-between items-center mb-2">
                <h4 className="font-semibold text-gray-800">句子 {index + 1}</h4>
                <span className={`font-semibold ${getScoreColor(result.score)}`}>得分: {result.score}%</span>
              </div>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="text-gray-500">您的答案：</span>
                  <p className="text-gray-800">{result.userInput}</p>
                </div>
                <div>
                  <span className="text-gray-500">正确答案：</span>
                  <p className="text-gray-800">{result.original}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <button onClick={handleReset} className="w-full mt-6 py-3 bg-gradient-to-r from-blue-500 to-emerald-500 text-white rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
          重新练习
        </button>
      </div>
    );
  }

  const currentResult = results[currentSentenceIndex];

  return (
    <div className="bg-white rounded-2xl shadow-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-xl font-semibold text-gray-900">听写练习</h3>
        <div className="flex items-center space-x-4">
          <span className="text-sm font-medium text-gray-500">
            {`句子 ${currentSentenceIndex + 1} / ${sentences.length}`}
          </span>
          <button onClick={handleReset} className="text-gray-500 hover:text-gray-700 transition-colors">
            <RotateCcw size={20} />
          </button>
        </div>
      </div>

      <audio ref={audioRef} src={currentSentenceAudio || ''} />

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
            请根据听到的内容输入当前句子：
            <button
              onClick={playCurrentSentence}
              disabled={isFetchingAudio || !currentSentenceAudio}
              className="p-1 rounded-full text-blue-600 hover:bg-blue-100 disabled:text-gray-400 disabled:cursor-wait transition-colors"
              aria-label="播放当前句子"
            >
              {isFetchingAudio ? <Loader2 size={20} className="animate-spin" /> : <Volume2 size={20} />}
            </button>
          </label>
          <textarea
            value={userInput}
            onChange={(e) => setUserInput(e.target.value)}
            disabled={isCurrentSubmitted}
            className="w-full h-32 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed"
            placeholder="在这里输入您听到的内容..."
          />
        </div>

        {!isCurrentSubmitted ? (
          <button
            onClick={handleSubmitCurrent}
            disabled={!userInput.trim()}
            className="w-full py-3 bg-gradient-to-r from-blue-500 to-emerald-500 text-white rounded-lg font-semibold hover:shadow-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            提交答案
          </button>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center justify-center space-x-2">
              {currentResult.score >= 70 ? <CheckCircle className="text-green-500" size={24} /> : <XCircle className="text-red-500" size={24} />}
              <span className={`text-xl font-semibold ${getScoreColor(currentResult.score)}`}>
                得分: {currentResult.score}%
              </span>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 mb-2">原文对照：</h4>
              <div className="space-y-2">
                <div>
                  <span className="text-sm text-gray-600">您的答案：</span>
                  <p className="text-gray-800">{currentResult.userInput}</p>
                </div>
                <div>
                  <span className="text-sm text-gray-600">正确答案：</span>
                  <p className="text-gray-800">{currentResult.original}</p>
                </div>
              </div>
            </div>
            
            <button
              onClick={handleNext}
              className="w-full py-3 bg-gray-700 text-white rounded-lg font-semibold hover:bg-gray-800 transition-all duration-300 flex items-center justify-center space-x-2"
            >
              <span>{currentSentenceIndex < sentences.length - 1 ? '下一句' : '完成练习'}</span>
              <ArrowRight size={20} />
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default DictationMode;
