// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://mmrqihdwqbmbvdbugvoe.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1tcnFpaGR3cWJtYnZkYnVndm9lIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4OTM5MzQsImV4cCI6MjA2NTQ2OTkzNH0.8iii64M6J4sM5R4nfkObLNu7QSNQhSlA5onDvZK3rJM";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);