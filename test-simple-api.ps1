# Simple API Test with Correct Endpoints
# Usage: .\test-simple-api.ps1 -ApiKey "your-api-key"

param(
    [Parameter(Mandatory=$true)]
    [string]$ApiKey
)

Write-Host "=== Simple API Test ===" -ForegroundColor Cyan
Write-Host "API Key: $($ApiKey.Substring(0,10))..." -ForegroundColor Yellow

# Test 1: Basic Text Generation (shortest timeout)
function Test-BasicText {
    param([string]$ApiKey)
    
    Write-Host "`n[TEXT] Testing basic text generation..." -ForegroundColor Green
    
    $headers = @{
        'Authorization' = "Bearer $ApiKey"
        'Content-Type' = 'application/json'
    }

    $body = @{
        model = "qwen-turbo"
        input = @{
            messages = @(
                @{
                    role = "user"
                    content = "Hi"
                }
            )
        }
        parameters = @{
            max_tokens = 10
        }
    } | ConvertTo-Json -Depth 10

    try {
        Write-Host "Sending request..." -ForegroundColor Yellow
        $response = Invoke-RestMethod -Uri 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation' -Method POST -Headers $headers -Body $body -TimeoutSec 10
        
        Write-Host "SUCCESS: Got response!" -ForegroundColor Green
        Write-Host "Response: $($response.output.text)" -ForegroundColor White
        return $true
    } catch {
        Write-Host "FAILED: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.ErrorDetails.Message) {
            try {
                $errorDetail = $_.ErrorDetails.Message | ConvertFrom-Json
                Write-Host "Error Code: $($errorDetail.code)" -ForegroundColor Yellow
                Write-Host "Error Message: $($errorDetail.message)" -ForegroundColor Yellow
            } catch {
                Write-Host "Raw Error: $($_.ErrorDetails.Message)" -ForegroundColor Yellow
            }
        }
        return $false
    }
}

# Test 2: Check API Key Validity
function Test-APIKey {
    param([string]$ApiKey)
    
    Write-Host "`n[API KEY] Testing API key validity..." -ForegroundColor Green
    
    $headers = @{
        'Authorization' = "Bearer $ApiKey"
        'Content-Type' = 'application/json'
    }

    # Try to get model list or make a minimal request
    try {
        Write-Host "Making minimal request..." -ForegroundColor Yellow
        $response = Invoke-WebRequest -Uri 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation' -Method POST -Headers $headers -Body '{"model":"qwen-turbo","input":{"messages":[{"role":"user","content":"test"}]},"parameters":{"max_tokens":1}}' -TimeoutSec 5
        
        Write-Host "API Key is valid! Status: $($response.StatusCode)" -ForegroundColor Green
        return $true
    } catch {
        $statusCode = $_.Exception.Response.StatusCode.value__
        Write-Host "Status Code: $statusCode" -ForegroundColor Yellow
        
        if ($statusCode -eq 401) {
            Write-Host "INVALID API KEY!" -ForegroundColor Red
        } elseif ($statusCode -eq 403) {
            Write-Host "API Key valid but access denied (permissions issue)" -ForegroundColor Yellow
        } elseif ($statusCode -eq 429) {
            Write-Host "Rate limited - API key is valid" -ForegroundColor Yellow
        } else {
            Write-Host "Other error: $($_.Exception.Message)" -ForegroundColor Yellow
        }
        return $false
    }
}

# Test 3: Network Connectivity
function Test-Network {
    Write-Host "`n[NETWORK] Testing network connectivity..." -ForegroundColor Green
    
    try {
        $response = Invoke-WebRequest -Uri 'https://dashscope.aliyuncs.com' -Method GET -TimeoutSec 5
        Write-Host "SUCCESS: Can reach DashScope servers" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "FAILED: Cannot reach DashScope servers" -ForegroundColor Red
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return $false
    }
}

# Test 4: Try Different Endpoints
function Test-Endpoints {
    param([string]$ApiKey)
    
    Write-Host "`n[ENDPOINTS] Testing different API endpoints..." -ForegroundColor Green
    
    $headers = @{
        'Authorization' = "Bearer $ApiKey"
        'Content-Type' = 'application/json'
    }
    
    $endpoints = @(
        @{ name = "Text Generation"; url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation" }
        @{ name = "Image Generation"; url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis" }
        @{ name = "Audio Generation"; url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text2speech/speech-synthesis" }
    )
    
    foreach ($endpoint in $endpoints) {
        try {
            Write-Host "Testing $($endpoint.name)..." -ForegroundColor Yellow
            $response = Invoke-WebRequest -Uri $endpoint.url -Method POST -Headers $headers -Body '{}' -TimeoutSec 3
            Write-Host "$($endpoint.name): Reachable (Status: $($response.StatusCode))" -ForegroundColor Green
        } catch {
            $statusCode = $_.Exception.Response.StatusCode.value__
            if ($statusCode -eq 400) {
                Write-Host "$($endpoint.name): Reachable (Bad Request - endpoint works)" -ForegroundColor Green
            } elseif ($statusCode -eq 401) {
                Write-Host "$($endpoint.name): Auth error" -ForegroundColor Red
            } elseif ($statusCode -eq 403) {
                Write-Host "$($endpoint.name): Access denied" -ForegroundColor Yellow
            } else {
                Write-Host "$($endpoint.name): Error $statusCode" -ForegroundColor Red
            }
        }
    }
}

# Run All Tests
Write-Host "`nRunning diagnostic tests..." -ForegroundColor Cyan

$results = @{
    Network = Test-Network
    APIKey = Test-APIKey -ApiKey $ApiKey
    Endpoints = Test-Endpoints -ApiKey $ApiKey
    BasicText = Test-BasicText -ApiKey $ApiKey
}

# Summary
Write-Host "`n=== Diagnostic Results ===" -ForegroundColor Cyan
Write-Host "Network Connectivity: $(if($results.Network){'✅ OK'}else{'❌ FAILED'})" -ForegroundColor $(if($results.Network){'Green'}else{'Red'})
Write-Host "API Key Validity: $(if($results.APIKey){'✅ OK'}else{'❌ FAILED'})" -ForegroundColor $(if($results.APIKey){'Green'}else{'Red'})
Write-Host "Basic Text Generation: $(if($results.BasicText){'✅ OK'}else{'❌ FAILED'})" -ForegroundColor $(if($results.BasicText){'Green'}else{'Red'})

Write-Host "`n=== Recommendations ===" -ForegroundColor Cyan

if (-not $results.Network) {
    Write-Host "🔧 Check your internet connection" -ForegroundColor Yellow
    Write-Host "🔧 Check if firewall is blocking requests" -ForegroundColor Yellow
}

if (-not $results.APIKey) {
    Write-Host "🔧 Verify your API key is correct and complete" -ForegroundColor Yellow
    Write-Host "🔧 Check if API key has expired" -ForegroundColor Yellow
}

if ($results.Network -and $results.APIKey -and -not $results.BasicText) {
    Write-Host "🔧 Your account may have usage restrictions" -ForegroundColor Yellow
    Write-Host "🔧 Try using the web interface to confirm services work" -ForegroundColor Yellow
}

if ($results.BasicText) {
    Write-Host "✅ Basic functionality works!" -ForegroundColor Green
    Write-Host "🔧 The timeout issues may be due to:" -ForegroundColor Yellow
    Write-Host "   - Network latency" -ForegroundColor White
    Write-Host "   - Account limitations requiring async calls" -ForegroundColor White
    Write-Host "   - Service-specific restrictions" -ForegroundColor White
}

Write-Host "`nDiagnostic completed!" -ForegroundColor Cyan
