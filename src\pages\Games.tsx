import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Gamepad2,
  Trophy,
  Target,
  Puzzle,
  Sparkles,
  PlayCircle,
  Timer,
  Star,
  Medal,
  Crown,
} from 'lucide-react';
import { Progress } from "@/components/ui/progress";

interface Game {
  id: string;
  icon: any;
  title: string;
  description: string;
  level: string;
  points: number;
  category: string;
}

const games: Game[] = [
  {
    id: 'word-puzzle',
    icon: Puzzle,
    title: '单词拼图',
    description: '通过拼图游戏学习新单词和短语',
    level: '初级',
    points: 100,
    category: '词汇'
  },
  {
    id: 'sentence-match',
    icon: Target,
    title: '句子匹配',
    description: '将英文句子与中文含义正确匹配',
    level: '中级',
    points: 150,
    category: '语法'
  },
  {
    id: 'audio-quest',
    icon: Sparkles,
    title: '听力冒险',
    description: '听音频回答问题，探索英语世界',
    level: '高级',
    points: 200,
    category: '听力'
  }
];

const Games = () => {
  const [selectedGame, setSelectedGame] = useState<Game | null>(null);
  const [gameContent, setGameContent] = useState('');
  const [loading, setLoading] = useState(false);
  const [score, setScore] = useState(0);
  const [level, setLevel] = useState(1);
  const [experience, setExperience] = useState(65);

  const startGame = async (game: Game) => {
    setSelectedGame(game);
    setLoading(true);
    try {
      const { data, error } = await supabase.functions.invoke('chat-ai', {
        body: {
          message: `请为"${game.title}"游戏生成一个练习题，包含问题和答案选项。难度：${game.level}`,
          conversationHistory: [],
          model: 'gemini-2.0-flash-exp',
        },
      });
      if (error) throw error;
      setGameContent(data.response);
    } catch (err: any) {
      setGameContent('AI服务出错，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-8">
      <div className="mb-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">游戏化学习</h1>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Trophy className="w-5 h-5 text-yellow-500" />
              <span className="font-medium">{score} 分</span>
            </div>
            <div className="flex items-center gap-2">
              <Crown className="w-5 h-5 text-purple-500" />
              <span className="font-medium">等级 {level}</span>
            </div>
          </div>
        </div>

        <Card className="p-4 bg-gradient-to-r from-purple-50 to-pink-50">
          <div className="flex items-center gap-4 mb-2">
            <Medal className="w-6 h-6 text-purple-500" />
            <div className="flex-1">
              <div className="flex justify-between mb-1">
                <span className="text-sm font-medium">经验值</span>
                <span className="text-sm text-gray-500">{experience}/100</span>
              </div>
              <Progress value={experience} className="h-2" />
            </div>
          </div>
        </Card>
      </div>

      {!selectedGame ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {games.map((game) => {
            const Icon = game.icon;
            return (
              <Card
                key={game.id}
                className="p-6 cursor-pointer hover:shadow-lg transition-shadow"
                onClick={() => startGame(game)}
              >
                <div className="flex justify-between items-start mb-4">
                  <div className="flex items-center gap-3">
                    <div className="p-3 bg-pink-100 rounded-lg">
                      <Icon className="w-6 h-6 text-pink-600" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold">{game.title}</h3>
                      <span className="text-sm text-gray-500">{game.category}</span>
                    </div>
                  </div>
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                    {game.level}
                  </span>
                </div>
                <p className="text-gray-600 mb-4">{game.description}</p>
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-1 text-yellow-500">
                    <Star className="w-4 h-4" />
                    <span className="text-sm">{game.points} 分</span>
                  </div>
                  <Button className="gap-2">
                    <PlayCircle className="w-4 h-4" />
                    开始游戏
                  </Button>
                </div>
              </Card>
            );
          })}
        </div>
      ) : (
        <Card className="p-6 max-w-4xl mx-auto">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-pink-100 rounded-lg">
                {React.createElement(selectedGame.icon, {
                  className: "w-6 h-6 text-pink-600"
                })}
              </div>
              <div>
                <h2 className="text-2xl font-semibold">{selectedGame.title}</h2>
                <span className="text-gray-500">{selectedGame.category} · {selectedGame.level}</span>
              </div>
            </div>
            <Button 
              className="gap-2"
              onClick={() => setSelectedGame(null)}
            >
              选择其他游戏
            </Button>
          </div>

          {loading ? (
            <div className="text-center py-8">加载游戏内容中...</div>
          ) : (
            <div className="space-y-6">
              <div className="prose max-w-none">
                {gameContent.split('\n').map((line, i) => (
                  <p key={i}>{line}</p>
                ))}
              </div>

              <div className="flex gap-4 justify-end">
                <Button className="gap-2">
                  <Timer className="w-4 h-4" />
                  重新开始
                </Button>
                <Button className="gap-2">
                  <Target className="w-4 h-4" />
                  提交答案
                </Button>
              </div>
            </div>
          )}
        </Card>
      )}
    </div>
  );
};

export default Games;
