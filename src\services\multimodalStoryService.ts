// 多模态故事生成服务 - 集成文本、图像和音频生成
import { alicloudService } from './alicloudService';
import { localAudioService } from './localAudioService';

export interface MultimodalStoryResult {
  id: string;
  title: string;
  description: string;
  story: string;
  storyChinese?: string;
  imageUrl?: string;
  audioUrl?: string;
  audioBlob?: Blob;
  createdAt: string;
}

export interface StoryGenerationOptions {
  generateImage?: boolean;
  generateAudio?: boolean;
  audioProvider?: 'alicloud' | 'minimax' | 'browser';
  imageStyle?: string;
  voiceId?: string;
}

class MultimodalStoryService {
  
  // 生成完整的多模态故事
  async generateMultimodalStory(
    prompt: string, 
    options: StoryGenerationOptions = {}
  ): Promise<MultimodalStoryResult> {
    const {
      generateImage = true,
      generateAudio = true,
      audioProvider = 'alicloud',
      imageStyle = 'illustration',
      voiceId
    } = options;

    try {
      console.log('开始生成多模态故事...');
      
      // 1. 生成故事文本和图像
      const storyResult = await this.generateStoryWithImage(prompt, generateImage, imageStyle);
      
      // 2. 生成音频（如果需要）
      let audioUrl: string | undefined;
      let audioBlob: Blob | undefined;
      
      if (generateAudio && storyResult.story) {
        try {
          const audioResult = await this.generateStoryAudio(
            storyResult.story, 
            audioProvider, 
            voiceId
          );
          audioUrl = audioResult.audioUrl;
          audioBlob = audioResult.audioBlob;
        } catch (error) {
          console.warn('音频生成失败，继续使用文本和图像:', error);
        }
      }

      // 3. 组装最终结果
      const result: MultimodalStoryResult = {
        id: `story-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        title: storyResult.title,
        description: storyResult.description,
        story: storyResult.story,
        storyChinese: storyResult.storyChinese,
        imageUrl: storyResult.imageUrl,
        audioUrl,
        audioBlob,
        createdAt: new Date().toISOString()
      };

      console.log('多模态故事生成完成');
      return result;

    } catch (error) {
      console.error('多模态故事生成失败:', error);
      throw error;
    }
  }

  // 生成故事文本和图像
  private async generateStoryWithImage(
    prompt: string, 
    generateImage: boolean, 
    imageStyle: string
  ) {
    // 构建增强的故事提示词
    const enhancedPrompt = `请根据以下提示创作一个英文故事，并提供中文翻译${generateImage ? '，同时生成图像描述' : ''}：

提示：${prompt}

请按照以下JSON格式返回：
{
  "title": "故事标题（英文）",
  "description": "故事简介（中文，50字以内）",
  "story": "完整的英文故事（200-500词）",
  "storyChinese": "中文翻译"${generateImage ? ',\n  "imagePrompt": "图像生成提示词（英文，描述故事的关键场景，适合' + imageStyle + '风格）"' : ''}
}

要求：
1. 故事要有趣且适合英语学习
2. 使用简单易懂的英语词汇
3. 包含对话和描述
4. 中文翻译要准确自然${generateImage ? '\n5. 图像提示词要生动具体，适合AI绘画' : ''}`;

    // 使用阿里云生成故事 (优先使用免费的Omni模型)
    const textResponse = await alicloudService.generateText(enhancedPrompt, 'qwen2.5-omni-7b');
    const parsedStory = this.parseStoryResponse(textResponse);

    // 生成图像（如果需要且有图像提示词）
    let imageUrl: string | undefined;
    if (generateImage && parsedStory.imagePrompt) {
      try {
        const imagePrompt = this.enhanceImagePrompt(parsedStory.imagePrompt, imageStyle);
        const imageResult = await alicloudService.generateImage(imagePrompt);
        imageUrl = imageResult.imageUrl;
        console.log('图像生成成功');
      } catch (error) {
        console.warn('图像生成失败:', error);
      }
    }

    return {
      ...parsedStory,
      imageUrl
    };
  }

  // 生成故事音频
  private async generateStoryAudio(
    storyText: string, 
    provider: string, 
    voiceId?: string
  ): Promise<{ audioUrl: string; audioBlob: Blob }> {
    
    if (provider === 'alicloud' && alicloudService.isConfigured()) {
      // 使用阿里云 TTS
      const audioResult = await alicloudService.generateAudio(storyText, voiceId || 'zhichu');
      const audioUrl = URL.createObjectURL(audioResult.audioBlob);
      return {
        audioUrl,
        audioBlob: audioResult.audioBlob
      };
    } else if (provider === 'minimax' && localAudioService.isConfigured()) {
      // 使用 MiniMax TTS
      const audioResult = await localAudioService.generateAudio(storyText);
      const audioUrl = URL.createObjectURL(audioResult.audioBlob);
      return {
        audioUrl,
        audioBlob: audioResult.audioBlob
      };
    } else {
      // 使用浏览器 TTS（不返回文件）
      await localAudioService.playTextDirectly(storyText);
      throw new Error('浏览器 TTS 不支持文件生成');
    }
  }

  // 增强图像提示词
  private enhanceImagePrompt(basePrompt: string, style: string): string {
    const stylePrompts = {
      'illustration': 'beautiful illustration, digital art, vibrant colors, detailed',
      'cartoon': 'cartoon style, cute, colorful, friendly characters',
      'realistic': 'photorealistic, high quality, detailed, natural lighting',
      'fantasy': 'fantasy art, magical, enchanting, mystical atmosphere',
      'watercolor': 'watercolor painting, soft colors, artistic, gentle brushstrokes',
      'anime': 'anime style, manga, Japanese animation, expressive characters'
    };

    const styleEnhancement = stylePrompts[style as keyof typeof stylePrompts] || stylePrompts.illustration;
    
    return `${basePrompt}, ${styleEnhancement}, high quality, masterpiece`;
  }

  // 解析故事响应
  private parseStoryResponse(response: string): {
    title: string;
    description: string;
    story: string;
    storyChinese?: string;
    imagePrompt?: string;
  } {
    try {
      // 尝试解析 JSON
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          title: parsed.title || '未命名故事',
          description: parsed.description || '一个有趣的故事',
          story: parsed.story || response,
          storyChinese: parsed.storyChinese,
          imagePrompt: parsed.imagePrompt
        };
      }
    } catch (error) {
      console.warn('JSON 解析失败，使用文本解析:', error);
    }

    // 如果 JSON 解析失败，使用文本解析
    return {
      title: this.extractTitle(response) || '生成的故事',
      description: '一个由AI生成的有趣故事',
      story: response,
      storyChinese: undefined
    };
  }

  // 从文本中提取标题
  private extractTitle(text: string): string | null {
    const titlePatterns = [
      /Title:\s*(.+)/i,
      /标题[：:]\s*(.+)/,
      /^(.+)$/m // 第一行作为标题
    ];

    for (const pattern of titlePatterns) {
      const match = text.match(pattern);
      if (match && match[1]) {
        return match[1].trim().substring(0, 50);
      }
    }

    return null;
  }

  // 获取支持的图像风格
  getSupportedImageStyles(): Array<{ id: string; name: string; description: string }> {
    return [
      { id: 'illustration', name: '插画风格', description: '数字艺术，色彩丰富，细节丰富' },
      { id: 'cartoon', name: '卡通风格', description: '可爱卡通，色彩鲜艳，友好角色' },
      { id: 'realistic', name: '写实风格', description: '照片级真实，高质量，自然光照' },
      { id: 'fantasy', name: '奇幻风格', description: '奇幻艺术，魔法，迷人神秘氛围' },
      { id: 'watercolor', name: '水彩风格', description: '水彩画，柔和色彩，艺术笔触' },
      { id: 'anime', name: '动漫风格', description: '日式动漫，漫画，表现力丰富' }
    ];
  }

  // 获取支持的音频提供商
  getSupportedAudioProviders(): Array<{ id: string; name: string; description: string }> {
    return [
      { id: 'alicloud', name: '阿里云 TTS', description: '高质量中文语音合成' },
      { id: 'minimax', name: 'MiniMax TTS', description: '专业级语音合成' },
      { id: 'browser', name: '浏览器 TTS', description: '免费但质量较低' }
    ];
  }

  // 检查服务可用性
  async checkServiceAvailability(): Promise<{
    text: boolean;
    image: boolean;
    audio: { alicloud: boolean; minimax: boolean; browser: boolean };
  }> {
    const result = {
      text: false,
      image: false,
      audio: {
        alicloud: false,
        minimax: false,
        browser: 'speechSynthesis' in window
      }
    };

    // 检查阿里云服务
    if (alicloudService.isConfigured()) {
      try {
        const testResult = await alicloudService.testConnection();
        result.text = testResult.text;
        result.image = testResult.image;
        result.audio.alicloud = testResult.audio;
      } catch (error) {
        console.warn('阿里云服务检查失败:', error);
      }
    }

    // 检查 MiniMax 服务
    if (localAudioService.isConfigured()) {
      try {
        result.audio.minimax = await localAudioService.testConnection();
      } catch (error) {
        console.warn('MiniMax 服务检查失败:', error);
      }
    }

    return result;
  }
}

// 创建单例实例
export const multimodalStoryService = new MultimodalStoryService();

// 导出类型和服务
export default MultimodalStoryService;
