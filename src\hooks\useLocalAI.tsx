import { useState, useCallback } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useToast } from '@/components/ui/use-toast';
import { localAIService, StoryGenerationResult } from '@/services/localAIService';
import { localAudioService } from '@/services/localAudioService';
import { useLocalStoryStorage } from '@/hooks/useLocalStoryStorage';
import { useLocalAudioStorage } from '@/hooks/useLocalAudioStorage';

// 故事类型定义
export interface Story {
  id: string;
  title: string;
  description: string;
  story: string;
  storyChinese?: string;
  imageUrl?: string;
  audioUrl?: string;
  prompt?: string;
  userId?: string;
  createdAt?: string;
  updatedAt?: string;
}

// 使用本地 AI 服务的 hooks
export const useLocalStories = () => {
  const { toast } = useToast();
  const { getLocalStories, saveStoryToLocal } = useLocalStoryStorage();
  const { saveAudioUrl } = useLocalAudioStorage();

  // 获取本地故事
  const { data: stories = [], isLoading } = useQuery({
    queryKey: ['local-stories'],
    queryFn: () => {
      const localStories = getLocalStories();
      return localStories.map(story => ({
        id: story.id,
        title: story.title,
        description: story.description,
        story: story.story,
        storyChinese: story.storyChinese,
        imageUrl: story.imageUrl,
        audioUrl: story.audioUrl,
        createdAt: new Date(story.createdAt).toISOString(),
        updatedAt: new Date(story.lastAccessed).toISOString(),
      }));
    },
  });

  // 生成故事
  const generateStoryMutation = useMutation({
    mutationFn: async (prompt: string): Promise<Story> => {
      if (!localAIService.isConfigured()) {
        throw new Error('AI 服务未配置，请先在设置中配置 API 密钥');
      }

      const aiData = await localAIService.generateStory(prompt);
      
      const newStory: Story = {
        id: `story-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        title: aiData.title,
        description: aiData.description,
        story: aiData.story,
        storyChinese: aiData.storyChinese,
        imageUrl: aiData.imageUrl,
        prompt: prompt,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      // 保存到本地存储
      saveStoryToLocal(newStory, false);

      return newStory;
    },
    onSuccess: (newStory) => {
      toast({
        title: "故事创作成功！",
        description: `快来看看关于"${newStory.title}"的新故事吧。`,
      });
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "故事生成失败",
        description: error.message,
      });
    }
  });

  return {
    stories,
    isLoading,
    generateStory: generateStoryMutation.mutate,
    isGenerating: generateStoryMutation.isPending,
  };
};

// 使用本地音频服务
export const useLocalAudio = () => {
  const { toast } = useToast();
  const { saveAudioUrl } = useLocalAudioStorage();

  // 生成音频
  const generateAudioMutation = useMutation({
    mutationFn: async ({ text, storyId }: { text: string; storyId?: string }): Promise<string> => {
      if (!text.trim()) {
        throw new Error('文本内容不能为空');
      }

      try {
        // 优先使用 MiniMax API
        if (localAudioService.isConfigured()) {
          const result = await localAudioService.generateAudio(text);
          const url = URL.createObjectURL(result.audioBlob);
          
          // 保存到本地存储
          if (storyId) {
            await saveAudioUrl(storyId, result.audioBlob);
          }
          
          return url;
        } else {
          // 使用浏览器原生 TTS 作为备选
          await localAudioService.playTextDirectly(text);
          return ''; // 浏览器 TTS 不返回文件
        }
      } catch (error) {
        console.warn('MiniMax TTS 失败，尝试浏览器 TTS:', error);
        // 如果 MiniMax 失败，回退到浏览器 TTS
        await localAudioService.playTextDirectly(text);
        return '';
      }
    },
    onSuccess: (url, { text }) => {
      if (url) {
        toast({
          title: "音频生成成功",
          description: "音频已保存到本地，现在可以播放了。",
        });
      } else {
        toast({
          title: "语音播放完成",
          description: "使用浏览器原生语音播放。",
        });
      }
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "音频生成失败",
        description: error.message,
      });
    }
  });

  // 直接播放文本
  const playTextMutation = useMutation({
    mutationFn: async (text: string) => {
      await localAudioService.playTextDirectly(text);
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "语音播放失败",
        description: error.message,
      });
    }
  });

  // 停止播放
  const stopPlayback = useCallback(() => {
    localAudioService.stopPlayback();
  }, []);

  return {
    generateAudio: generateAudioMutation.mutate,
    isGenerating: generateAudioMutation.isPending,
    playText: playTextMutation.mutate,
    isPlaying: playTextMutation.isPending,
    stopPlayback,
  };
};

// 使用本地聊天服务
export const useLocalChat = () => {
  const { toast } = useToast();
  const [conversationHistory, setConversationHistory] = useState<any[]>([]);

  // 发送消息
  const sendMessageMutation = useMutation({
    mutationFn: async (message: string) => {
      if (!localAIService.isConfigured()) {
        throw new Error('AI 服务未配置，请先在设置中配置 API 密钥');
      }

      const response = await localAIService.chat(message, conversationHistory);
      
      // 更新对话历史
      const newHistory = [
        ...conversationHistory,
        { role: 'user', content: message },
        { role: 'assistant', content: response.response }
      ];
      setConversationHistory(newHistory);

      return response;
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "AI 聊天失败",
        description: error.message,
      });
    }
  });

  // 清空对话历史
  const clearHistory = useCallback(() => {
    setConversationHistory([]);
  }, []);

  return {
    sendMessage: sendMessageMutation.mutate,
    isLoading: sendMessageMutation.isPending,
    conversationHistory,
    clearHistory,
    lastResponse: sendMessageMutation.data?.response,
  };
};

// 使用本地单词定义服务
export const useLocalWordDefinition = () => {
  const { toast } = useToast();

  // 获取单词定义
  const getDefinitionMutation = useMutation({
    mutationFn: async ({ word, context }: { word: string; context: string }) => {
      if (!localAIService.isConfigured()) {
        throw new Error('AI 服务未配置，请先在设置中配置 API 密钥');
      }

      return await localAIService.getWordDefinition(word, context);
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "查询单词失败",
        description: error.message,
      });
    }
  });

  return {
    getDefinition: getDefinitionMutation.mutate,
    isLoading: getDefinitionMutation.isPending,
    definition: getDefinitionMutation.data,
  };
};

// 检查服务状态
export const useServiceStatus = () => {
  const [status, setStatus] = useState({
    ai: false,
    audio: false,
    browserTTS: false,
  });

  const checkStatus = useCallback(async () => {
    const aiConfigured = localAIService.isConfigured();
    const audioConfigured = localAudioService.isConfigured();
    const browserTTSAvailable = 'speechSynthesis' in window;

    setStatus({
      ai: aiConfigured,
      audio: audioConfigured,
      browserTTS: browserTTSAvailable,
    });

    return {
      ai: aiConfigured,
      audio: audioConfigured,
      browserTTS: browserTTSAvailable,
    };
  }, []);

  return {
    status,
    checkStatus,
  };
};
