import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  BookOpen,
  Stars,
  GraduationCap,
  BookMarked,
  Search,
  PlayCircle,
  PlusCircle
} from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface Article {
  id: string;
  title: string;
  excerpt: string;
  level: 'beginner' | 'intermediate' | 'advanced';
  category: string;
  readingTime: string;
}

const sampleArticles: Article[] = [
  {
    id: '1',
    title: 'The Benefits of Learning a New Language',
    excerpt: 'Discover how learning a new language can enhance your cognitive abilities...',
    level: 'beginner',
    category: '文化',
    readingTime: '5 min'
  },
  {
    id: '2',
    title: 'Artificial Intelligence in Modern Life',
    excerpt: 'Explore how AI is transforming our daily lives and future prospects...',
    level: 'intermediate',
    category: '科技',
    readingTime: '8 min'
  },
  {
    id: '3',
    title: 'Climate Change: Global Challenges',
    excerpt: 'An in-depth analysis of climate change and its worldwide impact...',
    level: 'advanced',
    category: '环境',
    readingTime: '12 min'
  }
];

const Reading = () => {
  const [selectedLevel, setSelectedLevel] = useState<string>('all');
  const [selectedArticle, setSelectedArticle] = useState<Article | null>(null);
  const [analysis, setAnalysis] = useState('');
  const [loading, setLoading] = useState(false);
  const [vocabulary, setVocabulary] = useState<string[]>([]);

  const filteredArticles = selectedLevel === 'all'
    ? sampleArticles
    : sampleArticles.filter(article => article.level === selectedLevel);

  const analyzeArticle = async (article: Article) => {
    setSelectedArticle(article);
    setLoading(true);
    try {
      const { data, error } = await supabase.functions.invoke('chat-ai', {
        body: {
          message: `请分析以下英语文章，提供主要内容概述、重点词汇解释、语法重点分析：${article.title}\n${article.excerpt}`,
          conversationHistory: [],
          model: 'gemini-2.0-flash-exp',
        },
      });
      if (error) throw error;
      setAnalysis(data.response);
      
      // 模拟提取文章中的生词
      setVocabulary(['enhance', 'cognitive', 'transform', 'prospect', 'analysis']);
    } catch (err: any) {
      setAnalysis('AI服务出错，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-3xl font-bold mb-8 text-center">AI阅读助手</h1>

      <div className="flex flex-col lg:flex-row gap-8">
        {/* 左侧文章列表 */}
        <div className="lg:w-1/2">
          <Card className="p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold">推荐文章</h2>
              <Select
                value={selectedLevel}
                onValueChange={setSelectedLevel}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="选择难度等级" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部难度</SelectItem>
                  <SelectItem value="beginner">初级</SelectItem>
                  <SelectItem value="intermediate">中级</SelectItem>
                  <SelectItem value="advanced">高级</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-4">
              {filteredArticles.map((article) => (
                <Card
                  key={article.id}
                  className={`p-4 cursor-pointer hover:shadow-md transition-shadow ${
                    selectedArticle?.id === article.id ? 'ring-2 ring-blue-500' : ''
                  }`}
                  onClick={() => analyzeArticle(article)}
                >
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="text-lg font-medium">{article.title}</h3>
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                      {article.level}
                    </span>
                  </div>
                  <p className="text-gray-600 text-sm mb-3">{article.excerpt}</p>
                  <div className="flex items-center gap-4 text-sm text-gray-500">
                    <span className="flex items-center gap-1">
                      <BookMarked className="w-4 h-4" />
                      {article.category}
                    </span>
                    <span className="flex items-center gap-1">
                      <PlayCircle className="w-4 h-4" />
                      {article.readingTime}
                    </span>
                  </div>
                </Card>
              ))}
            </div>
          </Card>
        </div>

        {/* 右侧分析区域 */}
        <div className="lg:w-1/2">
          {selectedArticle ? (
            <Card className="p-6">
              <h2 className="text-xl font-semibold mb-4">阅读分析</h2>
              
              {loading ? (
                <div className="text-center py-8">分析中...</div>
              ) : (
                <div className="space-y-6">
                  <div className="prose max-w-none">
                    {analysis.split('\n').map((line, i) => (
                      <p key={i}>{line}</p>
                    ))}
                  </div>

                  {vocabulary.length > 0 && (
                    <div className="mt-6">
                      <h3 className="text-lg font-medium mb-3">生词本</h3>
                      <div className="flex flex-wrap gap-2">
                        {vocabulary.map((word, index) => (
                          <span
                            key={index}
                            className="px-3 py-1 bg-gray-100 rounded-full text-sm hover:bg-gray-200 cursor-pointer"
                          >
                            {word}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="flex gap-2">
                    <Button className="w-full">
                      <Stars className="w-4 h-4 mr-2" />
                      标记已读
                    </Button>
                    <Button className="w-full">
                      <GraduationCap className="w-4 h-4 mr-2" />
                      开始练习
                    </Button>
                  </div>
                </div>
              )}
            </Card>
          ) : (
            <div className="h-full flex items-center justify-center text-gray-500">
              ← 请选择一篇文章开始阅读
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Reading;
