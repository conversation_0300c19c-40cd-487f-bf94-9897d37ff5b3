
import React, { useEffect } from 'react';
import StoryCard from '@/components/stories/StoryCard';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { useStories } from '@/hooks/useStories';
import { sampleStories } from '@/data/sampleStories';
import { useLocalStoryStorage } from '@/hooks/useLocalStoryStorage';
import { useLocalAudioStorage } from '@/hooks/useLocalAudioStorage';
import StorageManager from '@/components/StorageManager';
import { useAuth } from '@/hooks/useAuth';
import { Link } from 'react-router-dom';

const formSchema = z.object({
  prompt: z.string().min(5, { message: '请输入至少5个字符来描述你的故事。' }),
});

const Stories = () => {
  const { user } = useAuth();
  const { stories, isLoading, generateStory, isGenerating } = useStories();
  const { getLocalStories, saveStoryToLocal, cleanupOldStories } = useLocalStoryStorage();
  const { cleanupOldAudio } = useLocalAudioStorage();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: { prompt: '' },
  });

  // 清理过期数据
  useEffect(() => {
    cleanupOldStories();
    cleanupOldAudio();
  }, [cleanupOldStories, cleanupOldAudio]);

  // 保存新生成的故事到本地
  useEffect(() => {
    stories.forEach(story => {
      saveStoryToLocal(story, false);
    });
  }, [stories, saveStoryToLocal]);

  function onSubmit(values: z.infer<typeof formSchema>) {
    generateStory(values.prompt);
    form.reset();
  }

  // 获取本地故事
  const localStories = getLocalStories();

  // 合并数据库故事、本地故事和示例故事，去重
  const allStoriesMap = new Map();

  // 添加数据库故事
  stories.forEach(story => allStoriesMap.set(story.id, story));

  // 添加本地故事（如果不在数据库中）
  localStories.forEach(story => {
    if (!allStoriesMap.has(story.id)) {
      allStoriesMap.set(story.id, story);
    }
  });

  // 添加示例故事
  sampleStories.forEach(story => {
    if (!allStoriesMap.has(story.id)) {
      allStoriesMap.set(story.id, story);
    }
  });

  const allStories = Array.from(allStoriesMap.values());

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 to-amber-50 py-12 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">加载故事中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-amber-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <div className="flex justify-between items-start mb-4">
            <div className="flex-1">
              <h1 className="text-4xl font-extrabold text-gray-900 sm:text-5xl">
                AI 故事屋
              </h1>
              <p className="mt-4 text-xl text-gray-600">
                聆听精彩的英文故事，在乐趣中提升你的英语能力。
              </p>
            </div>
            <div className="ml-4">
              <StorageManager />
            </div>
          </div>
        </div>
        
        <Card className="p-6 md:p-8 mb-12 max-w-2xl mx-auto shadow-lg">
          <h2 className="text-2xl font-bold text-center text-gray-800 mb-2">创建你自己的故事</h2>
          <p className="text-center text-gray-500 mb-6">
            输入一个简单的想法，AI将为你创作一个独特的故事。
            {!user && (
              <span className="block mt-1 text-sm text-orange-600">
                💡 提示：登录后可以保存和管理您的故事
              </span>
            )}
          </p>
          <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-col sm:flex-row items-start gap-2">
                  <FormField
                      control={form.control}
                      name="prompt"
                      render={({ field }) => (
                          <FormItem className="flex-grow w-full">
                              <FormControl>
                                  <Input placeholder="例如：一只想去月球旅行的勇敢小狐狸" {...field} className="py-6"/>
                              </FormControl>
                              <FormMessage />
                          </FormItem>
                      )}
                  />
                  <Button type="submit" disabled={isGenerating} className="w-full sm:w-36 py-6">
                      {isGenerating ? (
                          <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              生成中...
                          </>
                      ) : (
                          '开始创作'
                      )}
                  </Button>
              </form>
          </Form>
          {!user && (
            <div className="mt-4 text-center">
              <Link to="/login" className="text-blue-600 hover:text-blue-800 text-sm">
                登录以保存您的故事 →
              </Link>
            </div>
          )}
        </Card>

        {allStories.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">还没有故事，快来创建第一个故事吧！</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            {allStories.map((story) => (
              <StoryCard key={story.id} story={story} />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Stories;
