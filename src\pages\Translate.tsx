import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { BookmarkPlus, History, RotateCcw, Globe, ArrowLeftRight } from 'lucide-react';

interface TranslationHistory {
  from: string;
  to: string;
  text: string;
  translation: string;
  timestamp: Date;
}

const Translate = () => {
  const [input, setInput] = useState('');
  const [result, setResult] = useState('');
  const [loading, setLoading] = useState(false);
  const [fromLang, setFromLang] = useState<'zh' | 'en'>('zh');
  const [history, setHistory] = useState<TranslationHistory[]>([]);
  const [favorites, setFavorites] = useState<TranslationHistory[]>([]);

  const handleTranslate = async () => {
    if (!input.trim()) return;
    setLoading(true);
    setResult('');
    try {
      const { data, error } = await supabase.functions.invoke('chat-ai', {
        body: {
          message: `请${fromLang === 'zh' ? '将下列中文翻译成英文' : '将下列英文翻译成中文'}：${input}\n请同时解释翻译中的重点词汇和语法知识。`,
          conversationHistory: [],
          model: 'gemini-2.0-flash-exp',
        },
      });
      if (error) throw error;
      setResult(data.response);
      
      // 添加到历史记录
      const newHistory: TranslationHistory = {
        from: fromLang,
        to: fromLang === 'zh' ? 'en' : 'zh',
        text: input,
        translation: data.response,
        timestamp: new Date()
      };
      setHistory(prev => [newHistory, ...prev].slice(0, 10));
    } catch (err: any) {
      setResult('AI服务出错，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const switchLanguages = () => {
    setFromLang(prev => prev === 'zh' ? 'en' : 'zh');
    setInput('');
    setResult('');
  };

  const addToFavorites = () => {
    if (!result) return;
    const newFavorite: TranslationHistory = {
      from: fromLang,
      to: fromLang === 'zh' ? 'en' : 'zh',
      text: input,
      translation: result,
      timestamp: new Date()
    };
    setFavorites(prev => [newFavorite, ...prev]);
  };

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-3xl font-bold mb-8 text-center">智能翻译</h1>
      
      <Tabs defaultValue="translate" className="w-full max-w-4xl mx-auto">
        <TabsList className="grid w-full grid-cols-3 mb-8">
          <TabsTrigger value="translate">翻译</TabsTrigger>
          <TabsTrigger value="history">历史记录</TabsTrigger>
          <TabsTrigger value="favorites">收藏夹</TabsTrigger>
        </TabsList>

        <TabsContent value="translate">
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <Globe className="w-5 h-5" />
                <span className="font-medium">{fromLang === 'zh' ? '中文' : '英文'}</span>
              </div>
              <Button variant="outline" size="icon" onClick={switchLanguages}>
                <ArrowLeftRight className="w-4 h-4" />
              </Button>
              <div className="flex items-center gap-2">
                <Globe className="w-5 h-5" />
                <span className="font-medium">{fromLang === 'zh' ? '英文' : '中文'}</span>
              </div>
            </div>
            
            <div className="grid grid-cols-1 gap-4">
              <textarea
                className="w-full border rounded-lg p-4 min-h-[150px]"
                value={input}
                onChange={e => setInput(e.target.value)}
                placeholder={fromLang === 'zh' ? '请输入中文...' : 'Enter English text...'}
              />
              
              <div className="flex justify-between">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setInput('')}
                >
                  <RotateCcw className="w-4 h-4" />
                </Button>
                <Button
                  onClick={handleTranslate}
                  disabled={loading}
                >
                  {loading ? '翻译中...' : '翻译'}
                </Button>
                {result && (
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={addToFavorites}
                  >
                    <BookmarkPlus className="w-4 h-4" />
                  </Button>
                )}
              </div>

              {result && (
                <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                  <div className="prose max-w-none">
                    {result.split('\n').map((line, i) => (
                      <p key={i}>{line}</p>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="history">
          <Card className="p-6">
            <div className="space-y-4">
              {history.map((item, index) => (
                <div key={index} className="p-4 bg-gray-50 rounded-lg">
                  <div className="flex justify-between text-sm text-gray-500 mb-2">
                    <span>{item.from.toUpperCase()} → {item.to.toUpperCase()}</span>
                    <span>{new Date(item.timestamp).toLocaleString()}</span>
                  </div>
                  <p className="mb-2">{item.text}</p>
                  <p className="text-gray-600">{item.translation}</p>
                </div>
              ))}
              {history.length === 0 && (
                <p className="text-center text-gray-500">暂无历史记录</p>
              )}
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="favorites">
          <Card className="p-6">
            <div className="space-y-4">
              {favorites.map((item, index) => (
                <div key={index} className="p-4 bg-gray-50 rounded-lg">
                  <div className="flex justify-between text-sm text-gray-500 mb-2">
                    <span>{item.from.toUpperCase()} → {item.to.toUpperCase()}</span>
                    <span>{new Date(item.timestamp).toLocaleString()}</span>
                  </div>
                  <p className="mb-2">{item.text}</p>
                  <p className="text-gray-600">{item.translation}</p>
                </div>
              ))}
              {favorites.length === 0 && (
                <p className="text-center text-gray-500">暂无收藏记录</p>
              )}
            </div>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Translate;
