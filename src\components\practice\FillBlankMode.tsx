
import React, { useState, useEffect } from 'react';
import { CheckCircle, XCircle, RotateCcw } from 'lucide-react';

interface FillBlankModeProps {
  transcript: string;
  onComplete: (score: number, answers: string[]) => void;
  onReset: () => void;
}

const FillBlankMode = ({ transcript, onComplete, onReset }: FillBlankModeProps) => {
  const [blanks, setBlanks] = useState<{ word: string, index: number }[]>([]);
  const [userAnswers, setUserAnswers] = useState<string[]>([]);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [processedText, setProcessedText] = useState('');

  useEffect(() => {
    // 随机选择一些词汇作为填空
    const words = transcript.split(/\s+/);
    const selectedIndices = new Set<number>();
    const numBlanks = Math.min(5, Math.floor(words.length * 0.2)); // 20%的词汇作为填空
    
    while (selectedIndices.size < numBlanks) {
      const randomIndex = Math.floor(Math.random() * words.length);
      // 避免选择太短的词
      if (words[randomIndex].length > 3) {
        selectedIndices.add(randomIndex);
      }
    }
    
    const blankWords = Array.from(selectedIndices).map((index, blankIndex) => ({
      word: words[index].replace(/[.,!?;:]$/, ''), // 移除标点
      index: blankIndex
    }));
    
    setBlanks(blankWords);
    setUserAnswers(new Array(blankWords.length).fill(''));
    
    // 创建带有空白的文本
    let textWithBlanks = transcript;
    blankWords.forEach((blank, index) => {
      textWithBlanks = textWithBlanks.replace(
        new RegExp(`\\b${blank.word}\\b`, 'i'),
        `[${index + 1}]`
      );
    });
    
    setProcessedText(textWithBlanks);
  }, [transcript]);

  const handleAnswerChange = (index: number, value: string) => {
    const newAnswers = [...userAnswers];
    newAnswers[index] = value;
    setUserAnswers(newAnswers);
  };

  const handleSubmit = () => {
    setIsSubmitted(true);
    let correct = 0;
    blanks.forEach((blank, index) => {
      if (userAnswers[index].toLowerCase().trim() === blank.word.toLowerCase()) {
        correct++;
      }
    });
    
    const score = Math.round((correct / blanks.length) * 100);
    onComplete(score, userAnswers);
  };

  const handleReset = () => {
    setUserAnswers(new Array(blanks.length).fill(''));
    setIsSubmitted(false);
    onReset();
  };

  const getAnswerStatus = (index: number) => {
    if (!isSubmitted) return 'default';
    return userAnswers[index].toLowerCase().trim() === blanks[index].word.toLowerCase() ? 'correct' : 'incorrect';
  };

  return (
    <div className="bg-white rounded-2xl shadow-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-xl font-semibold text-gray-900">填空练习</h3>
        <button
          onClick={handleReset}
          className="text-gray-500 hover:text-gray-700 transition-colors"
        >
          <RotateCcw size={20} />
        </button>
      </div>

      <div className="space-y-6">
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-semibold text-gray-900 mb-2">原文（填空版）：</h4>
          <p className="text-gray-800 leading-relaxed whitespace-pre-wrap">
            {processedText}
          </p>
        </div>

        <div className="space-y-4">
          <h4 className="font-semibold text-gray-900">请填入空白处的词汇：</h4>
          {blanks.map((blank, index) => (
            <div key={index} className="flex items-center space-x-3">
              <span className="text-sm font-medium text-gray-600 w-8">
                {index + 1}.
              </span>
              <input
                type="text"
                value={userAnswers[index]}
                onChange={(e) => handleAnswerChange(index, e.target.value)}
                disabled={isSubmitted}
                className={`flex-1 p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed ${
                  isSubmitted 
                    ? getAnswerStatus(index) === 'correct'
                      ? 'border-green-500 bg-green-50'
                      : 'border-red-500 bg-red-50'
                    : 'border-gray-300'
                }`}
                placeholder="输入词汇..."
              />
              {isSubmitted && (
                <div className="w-6">
                  {getAnswerStatus(index) === 'correct' ? (
                    <CheckCircle className="text-green-500" size={20} />
                  ) : (
                    <XCircle className="text-red-500" size={20} />
                  )}
                </div>
              )}
            </div>
          ))}
        </div>

        {!isSubmitted ? (
          <button
            onClick={handleSubmit}
            disabled={userAnswers.some(answer => !answer.trim())}
            className="w-full py-3 bg-gradient-to-r from-blue-500 to-emerald-500 text-white rounded-lg font-semibold hover:shadow-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            提交答案
          </button>
        ) : (
          <div className="space-y-4">
            <div className="text-center">
              <span className="text-xl font-semibold text-gray-900">
                得分: {Math.round((blanks.filter((blank, index) => 
                  userAnswers[index].toLowerCase().trim() === blank.word.toLowerCase()
                ).length / blanks.length) * 100)}%
              </span>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 mb-2">答案对照：</h4>
              <div className="space-y-2">
                {blanks.map((blank, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <span className="text-sm text-gray-600">{index + 1}.</span>
                    <span className={`${
                      getAnswerStatus(index) === 'correct' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      您的答案: {userAnswers[index] || '(未填写)'}
                    </span>
                    <span className="text-gray-600">|</span>
                    <span className="text-gray-800">正确答案: {blank.word}</span>
                  </div>
                ))}
              </div>
            </div>
            
            <button
              onClick={handleReset}
              className="w-full py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
            >
              重新练习
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default FillBlankMode;
