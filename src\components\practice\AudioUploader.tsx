
import React from 'react';
import { Upload, X, Bot } from 'lucide-react';

interface AudioUploaderProps {
  onAudioUpload: (file: File) => void;
  onRemoveAudio: () => void;
  hasAudio: boolean;
  isUploading: boolean;
  onGenerateAudio: () => void;
  isGenerating: boolean;
}

const AudioUploader = ({ onAudioUpload, onRemoveAudio, hasAudio, isUploading, onGenerateAudio, isGenerating }: AudioUploaderProps) => {
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith('audio/')) {
      onAudioUpload(file);
    }
  };

  return (
    <div className="mb-4">
      {!hasAudio ? (
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
          <div className="grid md:grid-cols-2 gap-6 items-center">
            <div className="flex flex-col items-center justify-center space-y-2">
              <Upload className="mx-auto text-gray-400" size={32} />
              <p className="text-gray-600">上传本地音频</p>
              <input
                type="file"
                accept="audio/*"
                onChange={handleFileChange}
                className="hidden"
                id="audio-upload"
                disabled={isUploading || isGenerating}
              />
              <label
                htmlFor="audio-upload"
                className={`inline-block px-4 py-2 bg-blue-500 text-white rounded-lg cursor-pointer hover:bg-blue-600 transition-colors ${isUploading || isGenerating ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                {isUploading ? '上传中...' : '选择文件'}
              </label>
            </div>
            <div className="flex flex-col items-center justify-center space-y-2 md:border-l md:border-gray-200 pt-4 md:pt-0">
              <Bot className="mx-auto text-gray-400" size={32} />
              <p className="text-gray-600">使用 AI 生成</p>
              <button
                onClick={onGenerateAudio}
                disabled={isUploading || isGenerating}
                className="inline-block px-4 py-2 bg-emerald-500 text-white rounded-lg cursor-pointer hover:bg-emerald-600 transition-colors disabled:opacity-50"
              >
                {isGenerating ? '生成中...' : '开始生成'}
              </button>
            </div>
          </div>
          <p className="text-sm text-gray-500 mt-4 pt-4 border-t border-gray-200">
            您可以上传本地的 MP3, WAV, M4A 等格式，或使用 AI 根据原文生成朗读音频。
          </p>
        </div>
      ) : (
        <div className="flex items-center justify-between bg-green-50 border border-green-200 rounded-lg p-3">
          <span className="text-green-800">✓ 音频文件已就绪</span>
          <button
            onClick={onRemoveAudio}
            className="text-red-500 hover:text-red-700 transition-colors"
          >
            <X size={20} />
          </button>
        </div>
      )}
    </div>
  );
};

export default AudioUploader;
