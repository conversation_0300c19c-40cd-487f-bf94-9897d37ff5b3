
import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Home, BookOpen, MessageCircle, Headphones, LogIn, LogOut, User, CreditCard, Book, PenSquare, BookAudio } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/button';

const Navigation = () => {
  const location = useLocation();
  const { user, signOut } = useAuth();
  
  const navItems = [
    { path: '/', icon: Home, label: '首页' },
    { path: '/courses', icon: BookOpen, label: '课程' },
    { path: '/chat', icon: MessageCircle, label: 'AI对话' },
    { path: '/practice', icon: Headphones, label: '听力练习' },
    { path: '/stories', icon: BookAudio, label: 'AI 故事屋' },
    { path: '/vocabulary', icon: Book, label: '生词本' },
    { path: '/grammar', icon: PenSquare, label: '语法纠错' },
    { path: '/pricing', icon: CreditCard, label: '订阅' },
  ];

  return (
    <nav className="bg-white shadow-lg border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <Link to="/" className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent">
              EnglishAI
            </Link>
          </div>
          
          <div className="flex items-center">
            {/* Nav Items */}
            <div className="flex items-center space-x-1">
              {navItems.map((item) => {
                const Icon = item.icon;
                const isActive = location.pathname === item.path;
                return (
                  <Link
                    key={item.path}
                    to={item.path}
                    className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 text-sm font-medium ${
                      isActive
                        ? 'bg-gradient-to-r from-blue-500 to-emerald-500 text-white'
                        : 'text-gray-500 hover:text-blue-600 hover:bg-blue-50'
                    }`}
                  >
                    <Icon size={20} className="flex-shrink-0" />
                    <span className="hidden lg:inline">{item.label}</span>
                  </Link>
                );
              })}
            </div>
            
            {/* Auth Section */}
            <div className="flex items-center ml-4">
              {user ? (
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2 text-gray-600">
                    <User size={20} />
                    <span className="hidden sm:inline font-medium truncate max-w-[150px]">{user.email}</span>
                  </div>
                  <Button
                    onClick={signOut}
                    variant="outline"
                    size="sm"
                  >
                    <LogOut size={16} className="mr-0 lg:mr-2" />
                    <span className="hidden lg:inline">退出</span>
                  </Button>
                </div>
              ) : (
                <Link
                  to="/login"
                  className="flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-emerald-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-all duration-200"
                >
                  <LogIn size={20} />
                  <span>登录</span>
                </Link>
              )}
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;
