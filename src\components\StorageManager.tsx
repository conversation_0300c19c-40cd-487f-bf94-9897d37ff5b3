import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  HardDrive, 
  Trash2, 
  Download, 
  Upload, 
  RefreshCw,
  Music,
  FileText,
  Clock
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useLocalStoryStorage } from '@/hooks/useLocalStoryStorage';
import { useLocalAudioStorage } from '@/hooks/useLocalAudioStorage';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

const StorageManager = () => {
  const { toast } = useToast();
  const [storageStats, setStorageStats] = useState({
    totalStories: 0,
    storiesWithAudio: 0,
    storageSize: 0,
    lastCleanup: 0
  });
  const [audioStats, setAudioStats] = useState({
    count: 0,
    totalSize: 0
  });

  const {
    getStorageStats,
    cleanupOldStories,
    exportStoriesData,
    importStoriesData,
    getLocalStories,
    deleteLocalStory
  } = useLocalStoryStorage();

  const {
    getStorageInfo,
    cleanupOldAudio,
    getAllAudioData,
    deleteAudioUrl
  } = useLocalAudioStorage();

  // 加载存储统计信息
  const loadStats = async () => {
    const storyStats = getStorageStats();
    const audioInfo = await getStorageInfo();
    setStorageStats(storyStats);
    setAudioStats(audioInfo);
  };

  useEffect(() => {
    loadStats();
  }, []);

  // 清理过期数据
  const handleCleanup = async () => {
    try {
      cleanupOldStories();
      await cleanupOldAudio();
      await loadStats();
      toast({
        title: "清理完成",
        description: "已清理过期的故事和音频文件。",
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "清理失败",
        description: "清理过程中出现错误。",
      });
    }
  };

  // 导出数据
  const handleExport = () => {
    try {
      const data = exportStoriesData();
      const blob = new Blob([data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `speak-smart-stories-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      toast({
        title: "导出成功",
        description: "故事数据已导出到文件。",
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "导出失败",
        description: "导出过程中出现错误。",
      });
    }
  };

  // 导入数据
  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const jsonData = e.target?.result as string;
        const success = importStoriesData(jsonData);
        
        if (success) {
          loadStats();
          toast({
            title: "导入成功",
            description: "故事数据已成功导入。",
          });
        } else {
          throw new Error('导入失败');
        }
      } catch (error) {
        toast({
          variant: "destructive",
          title: "导入失败",
          description: "文件格式不正确或数据损坏。",
        });
      }
    };
    reader.readAsText(file);
    
    // 重置文件输入
    event.target.value = '';
  };

  // 格式化文件大小
  const formatSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 格式化时间
  const formatTime = (timestamp: number): string => {
    return new Date(timestamp).toLocaleString('zh-CN');
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <HardDrive className="mr-2 h-4 w-4" />
          存储管理
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <HardDrive className="mr-2 h-5 w-5" />
            本地存储管理
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* 存储统计 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">存储统计</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600 flex items-center">
                  <FileText className="mr-1 h-3 w-3" />
                  故事总数
                </span>
                <Badge variant="secondary">{storageStats.totalStories}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600 flex items-center">
                  <Music className="mr-1 h-3 w-3" />
                  含音频故事
                </span>
                <Badge variant="secondary">{storageStats.storiesWithAudio}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600 flex items-center">
                  <Music className="mr-1 h-3 w-3" />
                  音频文件数
                </span>
                <Badge variant="secondary">{audioStats.count}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">总大小</span>
                <Badge variant="outline">
                  {formatSize(storageStats.storageSize + audioStats.totalSize)}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* 操作按钮 */}
          <div className="grid grid-cols-2 gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleCleanup}
              className="flex items-center"
            >
              <RefreshCw className="mr-1 h-3 w-3" />
              清理过期
            </Button>
            
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleExport}
              className="flex items-center"
            >
              <Download className="mr-1 h-3 w-3" />
              导出数据
            </Button>
          </div>

          {/* 导入数据 */}
          <div>
            <label htmlFor="import-file" className="cursor-pointer">
              <Button 
                variant="outline" 
                size="sm" 
                className="w-full flex items-center"
                asChild
              >
                <span>
                  <Upload className="mr-1 h-3 w-3" />
                  导入数据
                </span>
              </Button>
            </label>
            <input
              id="import-file"
              type="file"
              accept=".json"
              onChange={handleImport}
              className="hidden"
            />
          </div>

          {/* 说明文字 */}
          <div className="text-xs text-gray-500 space-y-1">
            <p>• 故事和音频文件保存在浏览器本地存储中</p>
            <p>• 超过30天未访问的内容会被自动清理</p>
            <p>• 导出的数据可以在其他设备上导入</p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default StorageManager;
