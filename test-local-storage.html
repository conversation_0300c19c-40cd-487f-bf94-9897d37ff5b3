<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地存储测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>本地存储功能测试</h1>
    
    <div class="container">
        <h2>IndexedDB 音频存储测试</h2>
        <button onclick="testAudioStorage()">测试音频存储</button>
        <button onclick="getAudioInfo()">获取音频信息</button>
        <button onclick="clearAudioStorage()">清空音频存储</button>
        <div id="audioStatus"></div>
    </div>

    <div class="container">
        <h2>LocalStorage 故事存储测试</h2>
        <button onclick="testStoryStorage()">测试故事存储</button>
        <button onclick="getStoryInfo()">获取故事信息</button>
        <button onclick="clearStoryStorage()">清空故事存储</button>
        <div id="storyStatus"></div>
    </div>

    <script>
        // IndexedDB 音频存储测试
        const DB_NAME = 'SpeakSmartAudioDB';
        const DB_VERSION = 1;
        const STORE_NAME = 'audioFiles';

        function initDB() {
            return new Promise((resolve, reject) => {
                const request = indexedDB.open(DB_NAME, DB_VERSION);
                
                request.onerror = () => reject(request.error);
                request.onsuccess = () => resolve(request.result);
                
                request.onupgradeneeded = (event) => {
                    const db = event.target.result;
                    if (!db.objectStoreNames.contains(STORE_NAME)) {
                        const store = db.createObjectStore(STORE_NAME, { keyPath: 'id' });
                        store.createIndex('timestamp', 'timestamp', { unique: false });
                    }
                };
            });
        }

        async function testAudioStorage() {
            const statusDiv = document.getElementById('audioStatus');
            try {
                statusDiv.innerHTML = '<div class="info">正在测试音频存储...</div>';
                
                // 创建测试音频数据
                const testAudioData = new Blob(['test audio data'], { type: 'audio/mpeg' });
                const testId = 'test-story-' + Date.now();
                
                const db = await initDB();
                const transaction = db.transaction([STORE_NAME], 'readwrite');
                const store = transaction.objectStore(STORE_NAME);
                
                const audioData = {
                    id: testId,
                    blob: testAudioData,
                    url: URL.createObjectURL(testAudioData),
                    timestamp: Date.now(),
                    title: '测试故事'
                };
                
                await new Promise((resolve, reject) => {
                    const request = store.put(audioData);
                    request.onsuccess = () => resolve();
                    request.onerror = () => reject(request.error);
                });
                
                statusDiv.innerHTML = '<div class="success">✅ 音频存储测试成功！</div>';
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ 音频存储测试失败: ${error.message}</div>`;
            }
        }

        async function getAudioInfo() {
            const statusDiv = document.getElementById('audioStatus');
            try {
                const db = await initDB();
                const transaction = db.transaction([STORE_NAME], 'readonly');
                const store = transaction.objectStore(STORE_NAME);
                
                const allData = await new Promise((resolve, reject) => {
                    const request = store.getAll();
                    request.onsuccess = () => resolve(request.result);
                    request.onerror = () => reject(request.error);
                });
                
                let totalSize = 0;
                allData.forEach(data => {
                    totalSize += data.blob.size;
                });
                
                statusDiv.innerHTML = `
                    <div class="info">
                        📊 音频存储信息:<br>
                        - 文件数量: ${allData.length}<br>
                        - 总大小: ${(totalSize / 1024).toFixed(2)} KB<br>
                        - 数据库名: ${DB_NAME}
                    </div>
                `;
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ 获取音频信息失败: ${error.message}</div>`;
            }
        }

        async function clearAudioStorage() {
            const statusDiv = document.getElementById('audioStatus');
            try {
                const db = await initDB();
                const transaction = db.transaction([STORE_NAME], 'readwrite');
                const store = transaction.objectStore(STORE_NAME);
                
                await new Promise((resolve, reject) => {
                    const request = store.clear();
                    request.onsuccess = () => resolve();
                    request.onerror = () => reject(request.error);
                });
                
                statusDiv.innerHTML = '<div class="success">✅ 音频存储已清空！</div>';
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ 清空音频存储失败: ${error.message}</div>`;
            }
        }

        // LocalStorage 故事存储测试
        const STORAGE_KEY = 'speak_smart_stories';

        function testStoryStorage() {
            const statusDiv = document.getElementById('storyStatus');
            try {
                statusDiv.innerHTML = '<div class="info">正在测试故事存储...</div>';
                
                const testStory = {
                    id: 'test-story-' + Date.now(),
                    title: '测试故事',
                    description: '这是一个测试故事',
                    story: 'This is a test story.',
                    storyChinese: '这是一个测试故事。',
                    hasLocalAudio: false,
                    lastAccessed: Date.now(),
                    createdAt: Date.now()
                };
                
                const existingStories = JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]');
                existingStories.unshift(testStory);
                localStorage.setItem(STORAGE_KEY, JSON.stringify(existingStories));
                
                statusDiv.innerHTML = '<div class="success">✅ 故事存储测试成功！</div>';
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ 故事存储测试失败: ${error.message}</div>`;
            }
        }

        function getStoryInfo() {
            const statusDiv = document.getElementById('storyStatus');
            try {
                const stories = JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]');
                const storageSize = new Blob([localStorage.getItem(STORAGE_KEY) || '']).size;
                const storiesWithAudio = stories.filter(s => s.hasLocalAudio).length;
                
                statusDiv.innerHTML = `
                    <div class="info">
                        📊 故事存储信息:<br>
                        - 故事数量: ${stories.length}<br>
                        - 含音频故事: ${storiesWithAudio}<br>
                        - 存储大小: ${(storageSize / 1024).toFixed(2)} KB<br>
                        - 存储键: ${STORAGE_KEY}
                    </div>
                `;
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ 获取故事信息失败: ${error.message}</div>`;
            }
        }

        function clearStoryStorage() {
            const statusDiv = document.getElementById('storyStatus');
            try {
                localStorage.removeItem(STORAGE_KEY);
                statusDiv.innerHTML = '<div class="success">✅ 故事存储已清空！</div>';
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ 清空故事存储失败: ${error.message}</div>`;
            }
        }

        // 页面加载时显示当前存储状态
        window.onload = function() {
            getAudioInfo();
            getStoryInfo();
        };
    </script>
</body>
</html>
