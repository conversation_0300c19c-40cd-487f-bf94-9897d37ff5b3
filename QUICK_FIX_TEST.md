# 🚀 快速修复测试指南

## 🔧 **已修复的问题**

### ✅ **问题1: 文本模型显示"选择文本模型"**
- **修复**: 配置加载时自动设置默认模型
- **结果**: 现在应该显示 "qwen-turbo"

### ✅ **问题2: 音频连接测试失败**
- **修复**: 阿里云音频服务配置逻辑
- **结果**: 音频服务应该显示绿色 ✅

### ✅ **问题3: 音频优先级错误**
- **修复**: 优先使用阿里云TTS而不是MiniMax
- **结果**: 应该听到Cherry女声

## 🧪 **快速测试步骤**

### **第1步: 重启服务**
```bash
# 停止当前服务器 (Ctrl+C)
npm run dev
```

### **第2步: 清除缓存**
- 按 **F12** 打开开发者工具
- 右键点击刷新按钮 → **"清空缓存并硬性重新加载"**

### **第3步: 重新配置**
1. 点击右上角 **"API 配置"** 按钮
2. 确认配置：
   - **AI 提供商**: 阿里云 (推荐) ✅
   - **API Key**: [你的密钥] ✅
   - **文本模型**: 应该显示 **"qwen-turbo"** ✅ (不再是"选择文本模型")
   - **音频提供商**: 阿里云 Qwen-TTS (推荐) ✅
   - **API Key**: [你的密钥] ✅
   - **语音选择**: Cherry (女声) ✅

3. 点击 **"保存配置"**
4. 点击 **"测试连接"**

### **第4步: 验证连接状态**
应该看到：
- ✅ **AI 服务配置** - 已连接 (绿色)
- ✅ **音频服务配置** - 已连接 (绿色)

### **第5步: 测试故事生成**
1. 输入提示词: `一只勇敢的小猫咪`
2. 点击 **"开始创作"**
3. 等待故事生成完成

### **第6步: 测试音频播放**
1. 点击故事卡片的 **播放按钮** 🎵
2. 应该听到 **Cherry女声** 朗读故事

## 🎯 **成功标志**

### **配置界面**
- ✅ 文本模型显示 "qwen-turbo" (不是"选择文本模型")
- ✅ 两个绿色的 "已连接" 状态

### **控制台日志** (F12 → Console)
```
✅ 使用阿里云 Qwen-TTS 生成音频，语音: Cherry
✅ 音频下载成功，大小: [数字]
```

### **音频播放**
- ✅ 听到女声 (Cherry) 而不是男声
- ✅ 音频文件能正常下载

## 🔍 **如果仍有问题**

### **文本模型仍显示"选择文本模型"**
1. 关闭配置对话框
2. 刷新整个页面 (F5)
3. 重新打开配置

### **音频连接仍然失败**
1. 确认API Key正确
2. 检查控制台错误信息
3. 尝试重新输入API Key

### **音频仍是男声**
1. 确认音频提供商是 "阿里云 Qwen-TTS"
2. 确认语音选择是 "Cherry (女声)"
3. 清除浏览器本地存储:
   ```javascript
   // 在控制台执行
   localStorage.clear();
   location.reload();
   ```

## 🆘 **调试信息**

### **检查本地存储**
在控制台 (F12) 执行：
```javascript
console.log('AI配置:', JSON.parse(localStorage.getItem('ai_config') || '{}'));
console.log('音频配置:', JSON.parse(localStorage.getItem('audio_config') || '{}'));
```

应该看到：
```javascript
AI配置: {
  provider: "alicloud",
  model: "qwen-turbo",  // ✅ 不应该是空字符串
  apiKey: "sk-...",
  imageModel: "wanx2.1-t2i-turbo"
}

音频配置: {
  provider: "alicloud",  // ✅ 不应该是 "browser"
  voiceId: "Cherry",
  apiKey: "sk-...",
  speed: 1,
  volume: 1,
  pitch: 0
}
```

### **检查网络请求**
音频生成时，在 Network 标签应该看到：
```
POST /api/dashscope/api/v1/services/aigc/multimodal-generation/generation
Status: 200 ✅
Request Body: {
  "model": "qwen-tts",
  "input": {
    "text": "...",
    "voice": "Cherry"  // ✅ 应该是 Cherry
  }
}
```

## 🎉 **预期结果**

修复后应该看到：

1. **配置正确加载**: 文本模型显示具体名称
2. **连接测试成功**: 两个绿色状态
3. **音频使用阿里云**: Cherry女声播放
4. **控制台日志正常**: 显示正确的服务调用

如果以上都正常，说明修复成功！🎵✨

---

**💡 提示**: 如果问题仍然存在，请提供控制台的错误信息，我会进一步分析和修复。
